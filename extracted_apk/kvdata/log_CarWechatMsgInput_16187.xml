<?xml version="1.0" encoding="UTF-8"?>
<mmdatas package="com.tencent.mm.autogen.mmdata.rpt">

	<mmdata name="CarWechatMsgInput" logid="16187">
		<fields>
			<field id="21" name="MsgType" chinese="消息类型" type="int" default="0" remark=""/>
			<field id="22" name="OpType" chinese="用户操作类型" type="int" default="0" remark="0:收，1:发，2:登录，3:登出；目前只会报收的，发在另一条上报中"/>
			<field id="23" name="FromUser" chinese="发送者ID" type="char[1024]" default="0" remark=""/>
			<field id="24" name="ToUser" chinese="接受者ID" type="char[1024]" default="0" remark=""/>
			<field id="25" name="PushContent" chinese="消息摘要" type="char[1024]" default="0" remark=""/>
			<field id="26" name="MsgDealTime" chinese="消息处理时间" type="long" default="0" remark=""/>
			<field id="27" name="MsgDealChannel" chinese="消息处理渠道" type="int" default="0" remark="0:普通收发，1:api通知 目前只有0"/>
			<field id="28" name="MsgDealStatus" chinese="消息处理状态" type="int" default="0" remark="0:未读，1:已读"/>
			<field id="29" name="IsMute" chinese="发送者是否被置为静音" type="int" default="0" remark="0:否，1:是"/>
			<field id="30" name="IsBiz" chinese="发送者是否是公众号" type="int" default="0" remark="0:否，1:是"/>
			<field id="31" name="MsgTypeExt" chinese="消息类型" type="int" default="0" remark=""/>
			<field id="32" name="RecvTime" chinese="消息接收到的时间" type="long" default="0" remark=""/>

		</fields>
	</mmdata>
</mmdatas>
