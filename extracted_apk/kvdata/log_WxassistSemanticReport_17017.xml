<?xml version="1.0" encoding="UTF-8"?>
<mmdatas package="com.tencent.mm.autogen.mmdata.rpt">

	<mmdata name="WxassistSemanticReport" logid="17017" enable="false">
		<fields>
			<field id="20" name="ClientIPV6" chinese="客户端IPV6,系统字段,勿删" type="char[1024]" default=" " remark=""/>
			<field id="21" name="Package" chinese="第三方app的包名" type="char[1024]" default="" remark=""/>
			<field id="22" name="AppID" chinese="第三方app在公众平台注册的AppID" type="char[1024]" default="" remark=""/>
			<field id="23" name="ApiID" chinese="被调用的微信外部接口的ID" type="unsigned int" default="0" remark="59.通过文字识别语义意图"/>
			<field id="24" name="Return" chinese="接口返回码" type="unsigned int" default="0" remark="0 Appid,包名，签名MD5和后台拉取到的不一致； 1 成功； 2 参数错误； 3 未登录； 4 执行失败； 5 URI为null；6 包名为null； 7 AppID为null； 8 执行Event事件返回失败； 9 等待登录； 10 AppId在黑名单中； 11 没有接口权限；12 Crash； 13 AppInfo为空，等待微信拉取AppInfo； 14 客户端同步逻辑超时； 15 传入的ApiCode错误； 16 接口权限不受控；"/>
			<field id="25" name="TotalCostTime" chinese="调用总耗时" type="unsigned int" default="0" remark=""/>
			<field id="26" name="UserName" chinese="当前登陆的用户微信号" type="char[1024]" default="" remark=""/>
			<field id="27" name="UserId" chinese="传给后台的UserId" type="char[1024]" default="" remark=""/>
			<field id="28" name="Content" chinese="请求识别的文字内容" type="char[1024]" default="" remark=""/>
			<field id="29" name="ErrCode" chinese="后台接口返回码" type="unsigned int" default="0" remark="0 正确返回 其它：错误"/>
			<field id="30" name="ErrType" chinese="后台接口错误类型" type="unsigned int" default="0" remark=""/>
			<field id="31" name="ErrMsg" chinese="当前返回的错误信息" type="char[1024]" default="" remark=""/>
			<field id="32" name="RespTitle" chinese="后台返回的语义结果" type="char[1024]" default="" remark=""/>
			<field id="33" name="LocalIntent" chinese="本地识别出的意图" type="char[1024]" default="0" remark=""/>
			<field id="34" name="SematicInfo" chinese="语义返回整体信息" type="char[1024]" default="0" remark=""/>
			<field id="35" name="ContactsCount" chinese="联系人返回的个数(意图为搜索联系人的时候)" type="unsigned int" default="0" remark=""/>
			<field id="36" name="SematicCostTime" chinese="调用语义请求接口耗时" type="unsigned int" default="0" remark=""/>
			<field id="37" name="SentenceType" chinese="句式类型(对应content)" type="unsigned int" default="0" remark="0-未知 1-我要发微信或者发微信 2-发微信给xxx或者给xx发微信 4-打微信电话 5.给xx打微信语音电话或者打微信电话给xxx "/>
			<field id="38" name="ProductId" chinese="ilink注册时的productId，非负整数" type="int" default="-1" remark="-1-未知"/>
			<field id="39" name="BrandName" chinese="厂商" type="char[1024]" default="" remark=""/>
			<field id="40" name="CarModel" chinese="车型" type="char[1024]" default="" remark=""/>

		</fields>
	</mmdata>
</mmdatas>
