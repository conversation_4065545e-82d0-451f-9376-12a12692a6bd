<?xml version="1.0" encoding="UTF-8"?>
<mmdatas package="com.tencent.mm.autogen.mmdata.rpt">

	<mmdata name="CarRecorderReport" logid="17042" enable="false">
		<fields>
			<field id="20" name="ClientIPV6" chinese="客户端IPV6,系统字段,勿删" type="char[1024]" default=" " remark=""/>
			<field id="21" name="VoiceId" chinese="录音的voiceId" type="char[1024]" default="" remark=""/>
			<field id="22" name="StartRecordTime" chinese="录音开启时间" type="long long" default="0" remark=""/>
			<field id="23" name="InitFinishTime" chinese="录音初始化完成时间" type="long long" default="0" remark=""/>
			<field id="24" name="StartSucceTime" chinese="录音开启成功录音时间" type="long long" default="0" remark=""/>
			<field id="25" name="StartErrorTime" chinese="录音开启失败时间" type="long long" default="0" remark=""/>
			<field id="26" name="ErrCode" chinese="录音是否成功错误码" type="unsigned int" default="0" remark="0.录音成功 其它-录音失败"/>
			<field id="27" name="ErrType" chinese="录音错误类型" type="unsigned int" default="0" remark="1001-本地录音出错 其它-后台服务报错"/>
			<field id="28" name="Scene" chinese="场景值" type="char[1024]" default="0" remark=""/>
			<field id="29" name="ErrMsg" chinese="错误信息" type="char[1024]" default="" remark=""/>
			<field id="30" name="RecordMode" chinese="调用的录音接口" type="unsigned int" default="0" remark="0-系统自带录音接口 1-车联网提供的录音接口"/>
			<field id="31" name="InitPcmCostTime" chinese="初始化pcm耗时时长" type="unsigned int" default="0" remark=""/>
			<field id="32" name="InitSilkCostTime" chinese="初始化silk耗时时长" type="unsigned int" default="0" remark=""/>
			<field id="33" name="IniVadCostTime" chinese="初始化vad耗时" type="unsigned int" default="0" remark=""/>
			<field id="34" name="StartRecordCostTime" chinese="启动录音耗时" type="unsigned int" default="0" remark=""/>
			<field id="35" name="RecoStartCostTime" chinese="识别开始时间" type="long long" default="0" remark=""/>
			<field id="36" name="TotalRecordCostTime" chinese="录音总耗时" type="unsigned int" default="0" remark=""/>
			<field id="37" name="TotalRecoCostTime" chinese="后台识别总耗时" type="unsigned int" default="0" remark=""/>
			<field id="38" name="OnRecordFinTime" chinese="停止录音时间" type="unsigned int" default="0" remark=""/>
			<field id="39" name="OnRecoFinishTime" chinese="后台识别完成时间" type="unsigned int" default="0" remark=""/>
			<field id="40" name="LastReoCostTime" chinese="最后识别耗时" type="unsigned int" default="0" remark="RecoFinishTime-StopRecordFinTime时间"/>
			<field id="41" name="IsFinishReco" chinese="是否识别完成" type="int" default="" remark="是否收到尾包"/>
			<field id="42" name="HasRecoIntent" chinese="是否识别出意图" type="int" default="" remark=""/>
			<field id="43" name="RecoText" chinese="识别文字结果" type="char[1024]" default="" remark=""/>
			<field id="44" name="MaxAmp" chinese="最大振幅" type="int" default="" remark=""/>
			<field id="45" name="SpeakTimeSeconds" chinese="说话时长" type="int" default="" remark=""/>
			<field id="46" name="TotalTimeSeconds" chinese="总时长" type="int" default="" remark=""/>
			<field id="47" name="StopAndRecoReason" chinese="停止录音并识别原因" type="int" default="" remark="0=>无；1=>说话转静音,2=>空结果超时；3=>相同结果超时；4=>字数超长"/>
			<field id="48" name="ProductId" chinese="ilink注册时的productId，非负整数" type="int" default="-1" remark="-1-未知"/>
			<field id="49" name="BrandName" chinese="厂商" type="char[1024]" default="" remark=""/>
			<field id="50" name="CarModel" chinese="车型" type="char[1024]" default="" remark=""/>

		</fields>
	</mmdata>
</mmdatas>
