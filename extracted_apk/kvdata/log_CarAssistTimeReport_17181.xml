<?xml version="1.0" encoding="UTF-8"?>
<mmdatas package="com.tencent.mm.autogen.mmdata.rpt">

	<mmdata name="CarAssistTimeReport" logid="17181" enable="false">
		<fields>
			<field id="20" name="ClientIPV6" chinese="客户端IPV6,系统字段,勿删" type="char[1024]" default=" " remark=""/>
			<field id="21" name="VoiceId" chinese="请求的voiceId" type="unsigned int" default="0" remark=""/>
			<field id="22" name="Index" chinese="请求下标" type="unsigned int" default="0" remark=""/>
			<field id="23" name="ReqId" chinese="请求的id" type="unsigned int" default="0" remark=""/>
			<field id="24" name="ReqScene" chinese="请求的场景值" type="unsigned int" default="0" remark="类型弄错误了，字段用下面的"/>
			<field id="25" name="ReqOffset" chinese="请求语音数据的下标" type="unsigned int" default="0" remark=""/>
			<field id="26" name="ReqLen" chinese="请求语音数据的大小" type="unsigned int" default="0" remark=""/>
			<field id="27" name="ReqEndFlag" chinese="是否是请求尾包" type="unsigned int" default="0" remark="0-否 1-是"/>
			<field id="28" name="ReqTime" chinese="请求的时间点" type="long long" default="0" remark=""/>
			<field id="29" name="RespTime" chinese="返回的时间点" type="long long" default="0" remark=""/>
			<field id="30" name="CostTime" chinese="请求耗时" type="unsigned int" default="0" remark=""/>
			<field id="31" name="ReqPrevInterval" chinese="距离前一个请求的时间间隔" type="unsigned int" default="0" remark=""/>
			<field id="32" name="RespPrevInterval" chinese="距离前一个返回的时间间隔" type="unsigned int" default="0" remark=""/>
			<field id="33" name="Errcode" chinese="返回错误码" type="unsigned int" default="0" remark=""/>
			<field id="34" name="ErrType" chinese="返回的错误类型" type="unsigned int" default="0" remark=""/>
			<field id="35" name="IsRespFinalEnd" chinese="是否是返回的最后尾包结果" type="unsigned int" default="0" remark="0-否 1-是"/>
			<field id="36" name="Scene" chinese="场景值" type="char[1024]" default="0" remark=""/>
			<field id="37" name="RespText" chinese="返回的文字内容" type="char[1024]" default="0" remark=""/>
			<field id="38" name="ProductId" chinese="ilink注册时的productId，非负整数" type="int" default="-1" remark="-1-未知id"/>
			<field id="39" name="BrandName" chinese="厂商" type="char[1024]" default="" remark=""/>
			<field id="40" name="CarModel" chinese="车型" type="char[1024]" default="" remark=""/>

		</fields>
	</mmdata>
</mmdatas>
