<?xml version="1.0" encoding="UTF-8"?>
<mmdatas package="com.tencent.mm.autogen.mmdata.rpt">

	<mmdata name="CarWechatVoipReport" logid="17965">
		<fields>
			<field id="20" name="ClientIPV6" chinese="客户端IPV6,系统字段,勿删" type="char[1024]" default="" remark=""/>
			<field id="21" name="userName" chinese="用户微信ID" type="char[1024]" default="" remark=""/>
			<field id="22" name="errCode" chinese="是否正常呼起页面" type="int" default="0" remark="0-成功  -1Vui/Gui仲裁 -2正在通话"/>
			<field id="23" name="isOutCall" chinese="是否主叫" type="int" default="" remark="1-是      0-否"/>
			<field id="24" name="closeCause" chinese="关闭原因" type="int" default="" remark=""/>
			<field id="25" name="sceneId" chinese="sceneId" type="char[1024]" default="" remark=""/>
			<field id="26" name="lastStatus" chinese="关闭时最终状态" type="int" default="" remark=""/>
			<field id="27" name="closeErrCode" chinese="关闭Voip的错误原因" type="int" default="" remark="无用"/>
			<field id="28" name="mConnectSuccSec" chinese="通话时长" type="long" default="" remark="通话时长"/>
			<field id="29" name="ProductId" chinese="ilink注册时的productId，非负整数" type="int" default="-1" remark="-1-未知"/>
			<field id="30" name="BrandName" chinese="厂商" type="char[1024]" default="" remark=""/>
			<field id="31" name="CarModel" chinese="车型" type="char[1024]" default="" remark=""/>

		</fields>
	</mmdata>
</mmdatas>
