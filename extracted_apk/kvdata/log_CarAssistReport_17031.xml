<?xml version="1.0" encoding="UTF-8"?>
<mmdatas package="com.tencent.mm.autogen.mmdata.rpt">
	<mmdata name="CarAssistReport" logid="17031" enable="false">
		<fields>
			<field id="20" name="ClientIPV6" chinese="客户端IPV6,系统字段,勿删" type="char[1024]" default=" " remark=""/>
			<field id="21" name="VoiceId" chinese="语音识别ID" type="char[1024]" default="" remark=""/>
			<field id="22" name="ReqType" chinese="请求包的类型" type="unsigned int" default="0" remark="0.首包 2.中间包  3. 尾包"/>
			<field id="23" name="ReqNeedContact" chinese="是否需要通讯录做识别匹配" type="unsigned int" default="0" remark="0.不需要 1.需要"/>
			<field id="24" name="ReqAskContactServer" chinese="是否需要语音识别通讯录" type="unsigned int" default="0" remark="0.不需要 1.需要"/>
			<field id="25" name="ReqAppId" chinese="请求语音识别的AppId" type="char[1024]" default="0" remark=""/>
			<field id="26" name="ReqScene" chinese="请求的场景值" type="char[1024]" default="0" remark="空-其它控制命令 "/>
			<field id="27" name="ReqOffSet" chinese="请求的语音数据下标" type="unsigned int" default="0" remark=""/>
			<field id="28" name="ReqLen" chinese="请求语音数据的长度" type="unsigned int" default="0" remark=""/>
			<field id="29" name="ErrCode" chinese="返回错误码" type="unsigned int" default="0" remark=""/>
			<field id="30" name="ErrType" chinese="返回错误类型" type="unsigned int" default="0" remark=""/>
			<field id="31" name="ErrMsg" chinese="返回错误信息" type="char[1024]" default="" remark=""/>
			<field id="32" name="ErrFrom" chinese="错误的来源" type="unsigned int" default="0" remark="0.无 1-来自语音 2-来自语义"/>
			<field id="33" name="IsEnd" chinese="是否是结尾" type="unsigned int" default="0" remark="0.否 1-结尾"/>
			<field id="34" name="RecoText" chinese="语音识别结果" type="char[1024]" default="" remark=""/>
			<field id="35" name="SematicTitle" chinese="后台返回的语义结果" type="char[1024]" default="" remark=""/>
			<field id="36" name="LocalIntent" chinese="本地语义结果" type="char[1024]" default="" remark=""/>
			<field id="37" name="SematicInfo" chinese="语义的整体信息" type="char[1024]" default="" remark=""/>
			<field id="38" name="ContactsCount" chinese="搜索到联系人个数" type="unsigned int" default="0" remark=""/>
			<field id="39" name="FirstReqTime" chinese="首次请求时间点" type="long long" default="0" remark=""/>
			<field id="40" name="FirstGetContentTime" chinese="首次返回有内容的时间点" type="long long" default="0" remark=""/>
			<field id="41" name="TotalCostTime" chinese="整体耗时" type="unsigned int" default="0" remark=""/>
			<field id="42" name="LastCostTime" chinese="最后尾包耗时" type="unsigned int" default="0" remark="语义返回耗时"/>
			<field id="43" name="ReqUserId" chinese="请求的用户" type="char[1024]" default="" remark=""/>
			<field id="44" name="LastReqTime" chinese="最后尾包请求的时间点" type="long long" default="0" remark=""/>
			<field id="45" name="ProductId" chinese="ilink注册时的productId，非负整数" type="int" default="-1" remark="-1-未知"/>
			<field id="46" name="BrandName" chinese="厂商" type="char[1024]" default="" remark=""/>
			<field id="47" name="CarModel" chinese="车型" type="char[1024]" default="" remark=""/>

		</fields>
	</mmdata>
</mmdatas>
