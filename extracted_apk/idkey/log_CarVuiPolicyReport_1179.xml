<?xml version="1.0" encoding="UTF-8" ?>
<idkeys package="com.tencent.mm.autogen.idkey">
    <idkey name="CarVuiPolicyReport" id="1179" enable="false">
        <keys>
            <key name="VuiPolicyRequest" desc="VUI仲裁申请个数" key="0" />
            <key name="VuiPolicyRelease" desc="释放VUI仲裁个数" key="1" />
            <key name="VuiPolicyRequestSuccess" desc="VUI仲裁申请成功数" key="2" />
            <key name="VuiPolicyRequestFail" desc="VUI仲裁申请失败数" key="3" />
            <key name="VuiPolicyOnReject" desc="VUI仲裁收到onReject回调" key="4" />
            <key name="VuiPolicyOnRejectCloseNotifyMessage" desc="收消息页面被vui仲裁关闭" key="5" />
            <key name="VuiPolicyOnRejectCloseUnreadMessage" desc="未读消息页面被vui仲裁关闭" key="6" />
            <key name="VuiPolicyOnRejectCloseMain" desc="首页被vui仲裁关闭" key="7" />
            <key name="VuiPolicyOnRejectCloseVoip" desc="voip页面被vui仲裁关闭" key="8" />
            <key name="VuiPolicyOnRejectCloseVoipConfirm" desc="voip确认页被vui仲裁关闭" key="9" />
            <key name="VuiPolicyOnRejectCloseReply" desc="回复页面被vui仲裁关闭" key="10" />
            <key name="VuiPolicyOnRejectSendMessageFailPage" desc="发送消息失败提示页面收到vui仲裁 onReject回调"
                key="11" />
            <key name="VuiPolicyOnRejectOtherTipPage" desc="其他提示页收到vui仲裁onReject回调" key="12" />
        </keys>
    </idkey>
</idkeys>