<?xml version="1.0" encoding="UTF-8" ?>
<idkeys package="com.tencent.mm.autogen.idkey">
    <idkey name="CarGuiPolicyReport" id="1077" enable="false">
        <keys>
            <key name="GuiPolicyRequest" desc="GUI仲裁申请个数" key="0" />
            <key name="GuiPolicyRequestSuccess" desc="GUI仲裁申请成功" key="1" />
            <key name="GuiPolicyRequestFailMessage" desc="消息页面申请GUI仲裁失败" key="2" />
            <key name="GuiPolicyRequestFailOther" desc="其他页面申请GUI仲裁失败" key="3" />
            <key name="GuiPolicyOnGrant" desc="GUI仲裁收到onGrant回调" key="4" />
            <key name="GuiPolicyOnGrantOpenPage" desc="通过GUI onGrant回调，并且显示页面" key="5" />
            <key name="GuiPolicyOnReject" desc="GUI仲裁收到onReject回调" key="6" />
            <key name="GuiPolicyOnRejectClosePage" desc="GUI仲裁收到OnReject回调，并且关闭页面" key="7" />
        </keys>
    </idkey>
</idkeys>