<?xml version="1.0" encoding="UTF-8" ?>
<idkeys package="com.tencent.mm.autogen.idkey">
    <idkey name="RecognizeReport" id="1220" enable="false">
        <keys>
            <key name="RecordFail" desc="录音失败统计个数" key="0" />
            <key name="RecordFailInit" desc="录音初始化record失败" key="1" enable="false" />
            <key name="RecordFailInitSilk" desc="录音初始化silk失败" key="2" enable="false"/>
            <key name="RecordFailStart" desc="record启动start失败" key="3" enable="false"/>
            <key name="RecordFailCompress" desc="录音压缩出错" key="4" enable="false"/>
            <key name="RecordFailGetContent" desc="获取录音内容出错" key="5" enable="false"/>
            <key name="ServerNetworkTimeout" desc="后台接口网络超时" key="6" />
            <key name="ServerError" desc="后台接口失败报错次数" key="7" />
            <key name="StopRecordServerNotResponseFinalPack" desc="停止录音等待-识别回调超时（后台没有返回is_final_false）" key="8" />
            <key name="LocalError" desc="本地失败报错次数" key="9" />
            <key name="ServerFinishLocalGoOn" desc="后台直接到跳转到finish但是没有停止录音（由于网络延时，后台依然返回上次的结果）" key="10" />
        </keys>
    </idkey>
</idkeys>