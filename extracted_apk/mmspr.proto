package micromsg;

message BaseRequest {
  optional bytes SessionKey = 1;
  required uint32 Uin = 2;
  optional bytes DeviceID = 3;
  required int32 ClientVersion = 4;
  optional bytes DeviceType = 5;
  optional uint32 Scene = 6;
}

message BaseResponse {
  optional int32 Ret = 1;
  optional SKBuiltinString_t ErrMsg = 2;
}

message SKBuiltinString_t {
  optional string String = 1;
}

message AppVoiceControlVoiceAttr {
  required int32 FileType = 1;
  required int32 EncodeType = 2;
  required int32 SampleRate = 3;
  required int32 BitsPerSample = 4;
}

message AppVoiceControlUploadCtx {
  required uint32 TotalLen = 1;
  required uint32 StartPos = 2;
  required uint32 DataLen = 3;
}

message AppVoiceControlUploadCmd {
  required AppVoiceControlVoiceAttr VoiceAttr = 1;
  required AppVoiceControlUploadCtx UploadCtx = 2;
  required bytes VoiceData = 3;
}

message AppVoiceControlGetResultCmd {
  required bytes Cookies = 1;
}

message AppVoiceControlCookies {
  optional uint32 VoiceCommitTime = 1;
  optional uint32 VoiceQueryTimes = 2;
  optional string ResultCacheKey = 3;
}

message AppVoiceControlSearchContactResultItem {
  optional string Username = 1;
}

message AppVoiceControlSearchContactResult {
  repeated AppVoiceControlSearchContactResultItem Items = 1;
}

message AppVoiceControlResult {
  required uint32 VoiceId = 1;
  required uint32 ControlType = 2;
  required int32 RecognizeRet = 3;
  optional AppVoiceControlSearchContactResult SearchContactResultInfo = 4;
}

message AppVoiceControlRequest {
  required uint32 OpCode = 1;
  required string AppId = 2;
  required uint32 VoiceId = 3;
  required uint32 ControlType = 4;
  optional AppVoiceControlUploadCmd UploadCmd = 5;
  optional AppVoiceControlGetResultCmd GetResultCmd = 6;
}

message AppVoiceControlResponse {
  optional BaseResponse BaseResponse = 1;
  required uint32 QueryInterval = 2;
  required uint32 QueryTimeout = 3;
  optional bytes Cookies = 4;
  optional AppVoiceControlUploadCtx UploadCtx = 5;
  optional AppVoiceControlResult Result = 6;
}

message CheckCameraScanRequest {
  required BaseRequest BaseRequest = 1;
  optional string URL = 2;
  optional uint32 CodeType = 3;
  optional uint32 CodeVersion = 4;
}

message CheckCameraScanResponse {
  optional BaseResponse BaseResponse = 1;
  optional uint32 Type = 2;
}




message WXAssistSemanticReq
{
  required BaseRequest BaseRequest = 1;

  optional bytes app_id = 2;  // 可以填写openid等能区分产品的信息
  optional bytes user_id = 3; // 用户uin，必须可以区分不同用户；
  optional bytes query = 4;  // 用户的query，UTF8编码

  repeated bytes contact_list = 5; //通讯录列表
}

message Answer{
  optional bytes content = 1; //答案
  optional float confidence = 2; //置信度

  optional bytes response_title = 3; //知识点的标题
  optional bytes ans_node_name = 4; //意图名
}

message SlotItem    // 槽位详细信息
{
  required bytes slot_name = 1;   // 槽位名
  required bytes slot_value = 2;   // 槽位值
  required int32 start = 3;   // 起始位置
  required int32 end = 4;   // 结束位置
  required bytes confirm_status = 5;   //确认状态：“NONE/CONFIRMED/DENIED”
  required bytes norm = 6;   //归一化值
  required bytes entity_type = 7; //实体类型
  optional bytes norm_detail = 8; //norm值详细信息
}

message WXAssistSemanticResp
{
  optional BaseResponse BaseResponse = 1;

  repeated Answer answers = 2;// 答案列表，按置信度排序
  repeated SlotItem slot_item = 3; //槽位信息
}


message WXAssistVoiceRecoReq
{
  required BaseRequest BaseRequest = 1;

  optional bytes voice_id = 2; //一次语音识别任务的ID
  optional int32 type = 3;
  optional bytes app_id = 4; //对应的appid

  optional int32 voice_file_type = 5;   // 语音数据文件格式,用于处理文件头
  optional int32 voice_encode_type = 6;    // 语音数据编码格式，用于语音数据解码
  optional int32 samples_per_sec = 7;    // 语义数据采样率
  optional int32 bits_per_sample = 8;  // 样本数据大小，目前只支持16
  optional int32 max_result_count = 9;    // 要求返回识别结果的最大数量
  optional int32 result_type = 10;    // 返回结果类型

  //offset、len均是解压后语音的度量值
  optional int32 offset = 11; //该请求的语音数据在整个语音流中的offset
  optional int32 len = 12; //该请求的语音数据的长度
  optional bytes data = 13; //语音数据
  optional int32 need_contacts = 14;//是否拉取通讯录名单来辅助识别
  optional int32 ask_contact_server = 15;//同时请求通讯录识别服务，设置为1：返回通用识别结果和通讯录识别结果
}

message WXAssistVoiceRecoRsp
{
  optional BaseResponse BaseResponse = 1;
  optional int32 ret = 2; //错误码：0成功；否则为相应错误码；
  optional bytes voice_id = 3; //一次语音任务的ID
  optional bool is_end = 4;  //1: 识别结束
  optional bool is_req_end = 5; //1: 收到尾包，0: 不是尾包
  optional int32 ack_len = 6; // 该语音流期望收到的下条语音请求中语音数据的len
  optional int32 ack_offset = 7; // 该语音流期望收到的下条语音请求中语音数据的offset
  repeated RecoRes reco_res = 8;  //返回的识别结果
  optional bool is_final_res = 9; //1:最终的识别结果 0:不是最终的识别结果
  optional WXAssistVoiceContactRsp contact_res = 12;
}

message RecoRes
{
  optional bytes text = 1;
  optional float confidence = 2;
  optional float similarity = 3;
  optional bytes comment = 4;
  repeated Word word = 5;
}

message Word
{
  optional bytes word = 1;
  optional float confidence = 2;
  optional int32 mbtm = 3;
  optional int32 metm = 4;
}

//from service to cgi
message WXAssistVoiceContactRsp
{
  optional int32 ret = 1; //是否处理成功；0成功；-1失败；
  optional bytes voice_id = 2; //一次语音任务的ID
  optional int32 sr_seq = 3;
  optional int32 type = 4; //语音类型：开始、中间、结束、整体
  optional int32 err_no = 5; // 返回的错误号
  repeated RecoRes reco_res = 6;  //返回的识别结果
}

message CarAssistReq{
  required BaseRequest BaseRequest = 1;
  optional WXAssistVoiceRecoReq vr_req = 2;

  optional bytes scene = 3; //当前场景 空-其它控制命令 "MessageBody"-消息体 "SearchContact"-搜索联系人
  optional bytes app_id = 4;  // 可以填写openid等能区分产品的信息
  optional bytes user_id = 5; // 用户uin，必须可以区分不同用户；
}

message CarAssistResp{
  optional BaseResponse BaseResponse = 1;

  optional WXAssistVoiceRecoRsp vr_resp = 2;
  optional WXAssistSemanticResp semantic_res = 3;
  optional float vr_time = 4;
  optional float sem_time = 5;
}


//用openid去开平获取username
message TransIdRequest {
  required BaseRequest BaseRequest = 1;
  optional string appid = 2;
  optional string username = 3;
  optional string openid = 4;
}

message TransIdResponse {
  optional BaseResponse BaseResponse = 1;
  optional string openid = 2;
  optional string username = 3;
}

message CarTextToSpeechReq{
  required BaseRequest BaseRequest = 1;
  optional string utf8_text = 2;
  optional string model = 3;
  optional uint32 format = 4; // 0: pcm 1: wav 2: mp3
  optional uint32 head_silence = 5; //ms
  optional uint32 tail_silence = 6; //ms
}

message CarTextToSpeechResp{
  required BaseResponse BaseResponse = 1;
  optional bytes data = 2;
}

