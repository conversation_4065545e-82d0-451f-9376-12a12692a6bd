# Properties file which configures the operation of the JDK logging facility.
#
# The configuration in this file is the suggesgted configuration
# for collecting trace for helping debug problems related to the
# Paho MQTT client.  It configures trace to be continuosly collected
# in memory with minimal impact on performance. 
# 
# When the push trigger (by default a Severe level message) or a 
# specific request is made to "push" the in memory trace then it 
# is "pushed" to the configured target handler. By default
# this is the standard java.util.logging.FileHandler. The Paho Debug 
# class can be used to push the memory trace to its target 
# 
# To enable trace either:
# - use this properties file as is and set the logging facility up 
#   to use it by configuring the util logging system property e.g.
#
# >java -Djava.util.logging.config.file=<location>\jsr47min.properties
#
# - This contents of this file can also be merged with another
#   java.util.logging config file to ensure provide wider logging 
#   and trace including Paho trace 

# Global logging properties.
# ------------------------------------------
# The set of handlers to be loaded upon startup.
# Comma-separated list of class names.
# - Root handlers are not enabled by default - just handlers on the Paho packages. 
#handlers=java.util.logging.MemoryHandler,java.util.logging.FileHandler, java.util.logging.ConsoleHandler

# Default global logging level.
# Loggers and Handlers may override this level
#.level=INFO

# Loggers
# ------------------------------------------
# A memoryhandler is attached to the paho packages
# and the level specified to collected all trace related
# to paho packages.  This will override any root/global
# level handlers if set.  
org.eclipse.paho.mqttv5.client.handlers=java.util.logging.MemoryHandler
org.eclipse.paho.mqttv5.client.level=ALL
# It is possible to set more granular trace on a per class basis e.g.
#org.eclipse.paho.mqttv5.client.internal.ClientComms.level=ALL

# Handlers
# -----------------------------------------
# Note: the target handler that is associated with the MemoryHandler is not a root handler 
# and hence not returned when getting the handlers from root. It appears accessing 
# target handler programatically is not possible as target is a private variable in 
# class MemoryHandler
java.util.logging.MemoryHandler.level=ALL
java.util.logging.MemoryHandler.size=10000
java.util.logging.MemoryHandler.push=ALL
java.util.logging.MemoryHandler.target=java.util.logging.FileHandler
#java.util.logging.MemoryHandler.target=java.util.logging.ConsoleHandler


# --- FileHandler ---
# Override of global logging level
java.util.logging.FileHandler.level=ALL

# Naming style for the output file:
# (The output file is placed in the directory
# defined by the "user.home" System property.)
# See java.util.logging for more options 
java.util.logging.FileHandler.pattern=%h/ibm/paho/trace/paho%u.log

# Limiting size of output file in bytes:
java.util.logging.FileHandler.limit=200000

# Number of output files to cycle through, by appending an
# integer to the base file name:
java.util.logging.FileHandler.count=3

java.util.logging.FileHandler.formatter=org.eclipse.paho.client.mqttv3.logging.SimpleLogFormatter

# --- ConsoleHandler ---
# Override of global logging level
#java.util.logging.ConsoleHandler.level=INFO
#java.util.logging.ConsoleHandler.formatter=java.util.logging.SimpleFormatter
#java.util.logging.ConsoleHandler.formatter=org.eclipse.paho.client.mqttv3.logging.SimpleLogFormatter
