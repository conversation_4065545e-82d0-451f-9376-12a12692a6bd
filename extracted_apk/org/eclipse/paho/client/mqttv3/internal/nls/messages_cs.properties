#/* 
# * Copyright (c) 2009, 2012 IBM Corp.
# *
# * All rights reserved. This program and the accompanying materials
# * are made available under the terms of the Eclipse Public License v1.0
# * which accompanies this distribution, and is available at
# * http://www.eclipse.org/legal/epl-v10.html
# *
# * Contributors:
# *    Dave Locke - initial API and implementation and/or initial documentation
# */
# NLS_MESSAGEFORMAT_VAR
# NLS_ENCODING=UNICODE
1=Neplatn\u00e1 verze protokolu
2=Neplatn\u00e9 ID klienta
3=Nedostupn\u00fd zprost\u0159edkovatel
4=Chybn\u00e9 jm\u00e9no u\u017eivatele nebo heslo
5=Chyb\u00ed autorizace pro p\u0159ipojen\u00ed
6=Neo\u010dek\u00e1van\u00e1 chyba
32000=Vypr\u0161en\u00ed \u010dasov\u00e9ho limitu pro odpov\u011b\u010f ze serveru
32100=Klient je p\u0159ipojen
32101=Klient je odpojen
32102=Klient se aktu\u00e1ln\u011b odpojuje
32103=Nelze se p\u0159ipojit k serveru
32104=Klient nen\u00ed p\u0159ipojen
32105=Ur\u010den\u00fd typ polo\u017eky SocketFactory neodpov\u00edd\u00e1 identifik\u00e1toru URI zprost\u0159edkovatele.
32106=Chyba konfigurace zabezpe\u010den\u00ed SSL
32107=Z metody zp\u011btn\u00e9ho vol\u00e1n\u00ed nen\u00ed povoleno odpojen\u00ed
32108=Nerozpoznan\u00fd paket
32109=P\u0159ipojen\u00ed bylo ztraceno.
32110=P\u0159ipojen\u00ed ji\u017e prob\u00edh\u00e1
32111=Klient je zav\u0159en
32200=Perzistence je ji\u017e pou\u017e\u00edv\u00e1na.
32201=Token se ji\u017e pou\u017e\u00edv\u00e1
32202=Prob\u00edh\u00e1 p\u0159\u00edli\u0161 mnoho publikov\u00e1n\u00ed
