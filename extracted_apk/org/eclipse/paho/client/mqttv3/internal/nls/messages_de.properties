#/* 
# * Copyright (c) 2009, 2012 IBM Corp.
# *
# * All rights reserved. This program and the accompanying materials
# * are made available under the terms of the Eclipse Public License v1.0
# * which accompanies this distribution, and is available at
# * http://www.eclipse.org/legal/epl-v10.html
# *
# * Contributors:
# *    Dave <PERSON> - initial API and implementation and/or initial documentation
# */
# NLS_MESSAGEFORMAT_VAR
# NLS_ENCODING=UNICODE
1=Protokollversion ung\u00fcltig
2=Client-ID ung\u00fcltig
3=Broker nicht verf\u00fcgbar
4=Benutzername oder Kennwort falsch
5=Keine Berechtigung f\u00fcr Verbindung
6=Unerwarteter Fehler
32000=Zeitlimit\u00fcberschreitung beim Warten auf eine Antwort vom Server
32100=Verbindung zu Client ist hergestellt
32101=Verbindung zu Client ist getrennt
32102=Verbindung zu Client wird derzeit getrennt
32103=Verbindung zu Server kann nicht hergestellt werden
32104=Keine Verbindung zu Client
32105=Der angegebene Socket-Factorytyp entspricht nicht der Broker-URI
32106=SSL-Konfigurationsfehler
32107=Trennung einer Verbindung \u00fcber eine Callback-Methode ist nicht zul\u00e4ssig
32108=Paket nicht erkannt
32109=Verbindung wurde getrennt
32110=Verbindungsherstellung wird ausgef\u00fchrt
32111=Client ist geschlossen
32200=Persistenz wird bereits verwendet
32201=Token wird bereits verwendet
32202=Zu viele Ver\u00f6ffentlichungen werden ausgef\u00fchrt
