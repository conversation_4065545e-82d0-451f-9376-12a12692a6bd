#/* 
# * Copyright (c) 2009, 2012 IBM Corp.
# *
# * All rights reserved. This program and the accompanying materials
# * are made available under the terms of the Eclipse Public License v1.0
# * which accompanies this distribution, and is available at
# * http://www.eclipse.org/legal/epl-v10.html
# *
# * Contributors:
# *    Dave Locke - initial API and implementation and/or initial documentation
# */
# NLS_MESSAGEFORMAT_VAR
# NLS_ENCODING=UNICODE
1=Vers\u00e3o de protocolo inv\u00e1lida
2=ID de cliente inv\u00e1lido
3=Broker indispon\u00edvel
4=Nome de usu\u00e1rio ou senha inv\u00e1lidos
5=N\u00e3o autorizado a conectar
6=Erro inesperado
32000=Tempo limite atingido ao aguardar por uma resposta do servidor
32100=O cliente est\u00e1 conectado
32101=O cliente est\u00e1 desconectado
32102=Cliente desconectando atualmente
32103=N\u00e3o \u00e9 poss\u00edvel se conectar ao servidor
32104=O cliente n\u00e3o est\u00e1 conectado
32105=O tipo SocketFactory especificado n\u00e3o corresponde ao URI do broker
32106=Erro de configura\u00e7\u00e3o de SSL
32107=A desconex\u00e3o n\u00e3o \u00e9 permitida a partir de um m\u00e9todo de retorno de chamada
32108=Pacote n\u00e3o reconhecido
32109=Conex\u00e3o perdida
32110=A conex\u00e3o j\u00e1 est\u00e1 em andamento
32111=O cliente foi encerrado
32200=Persist\u00eancia j\u00e1 em uso
32201=O token j\u00e1 est\u00e1 em uso
32202=Muitas publica\u00e7\u00f5es em andamento
