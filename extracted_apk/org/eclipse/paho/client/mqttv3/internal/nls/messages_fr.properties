#/* 
# * Copyright (c) 2009, 2012 IBM Corp.
# *
# * All rights reserved. This program and the accompanying materials
# * are made available under the terms of the Eclipse Public License v1.0
# * which accompanies this distribution, and is available at
# * http://www.eclipse.org/legal/epl-v10.html
# *
# * Contributors:
# *    <PERSON> - initial API and implementation and/or initial documentation
# */
# NLS_MESSAGEFORMAT_VAR
# NLS_ENCODING=UNICODE
1=Version de protocole incorrecte
2=ID client incorrect
3=Courtier indisponible
4=Nom d'utilisateur ou mot de passe incorrect
5=L'utilisateur n'est pas autoris\u00e9 \u00e0 se connecter
6=Erreur inattendue.
32000=Expiration du d\u00e9lai d'attente d'une r\u00e9ponse du serveur
32100=Client connect\u00e9
32101=Client d\u00e9connect\u00e9
32102=Client en cours de d\u00e9connexion
32103=Impossible de se connecter au serveur
32104=Client non connect\u00e9
32105=Le type SocketFactory sp\u00e9cifi\u00e9 ne correspond pas \u00e0 l'URI de courtier
32106=Erreur de configuration SSL
32107=D\u00e9connexion non autoris\u00e9e pour une m\u00e9thode de rappel
32108=Paquet non reconnu
32109=Connexion perdue
32110=Connexion d\u00e9j\u00e0 en cours
32111=Client ferm\u00e9
32200=La persistance est d\u00e9j\u00e0 en cours d'utilisation
32201=Jeton d\u00e9j\u00e0 en cours d'utilisation
32202=Trop de publications en cours
