#/* 
# * Copyright (c) 2009, 2012 IBM Corp.
# *
# * All rights reserved. This program and the accompanying materials
# * are made available under the terms of the Eclipse Public License v1.0
# * which accompanies this distribution, and is available at
# * http://www.eclipse.org/legal/epl-v10.html
# *
# * Contributors:
# *    Dave <PERSON> - initial API and implementation and/or initial documentation
# */
# NLS_MESSAGEFORMAT_VAR
# NLS_ENCODING=UNICODE
1=Niepoprawna wersja protoko\u0142u
2=Niepoprawny identyfikator klienta
3=Broker niedost\u0119pny
4=Niepoprawna nazwa u\u017cytkownika lub has\u0142o
5=Brak autoryzacji do nawi\u0105zania po\u0142\u0105czenia
6=Nieoczekiwany b\u0142\u0105d
32000=Przekroczono limit czasu oczekiwania na odpowied\u017a z serwera
32100=Po\u0142\u0105czenie z klientem zosta\u0142o nawi\u0105zane
32101=Po\u0142\u0105czenie z klientem zosta\u0142o roz\u0142\u0105czone
32102=Klient roz\u0142\u0105cza si\u0119
32103=Nie mo\u017cna nawi\u0105za\u0107 po\u0142\u0105czenia z serwerem
32104=Po\u0142\u0105czenie z klientem nie jest nawi\u0105zane
32105=Podany typ fabryki SocketFactory nie jest zgodny z identyfikatorem URI brokera
32106=B\u0142\u0105d konfiguracji protoko\u0142u SSL
32107=Roz\u0142\u0105czenie nie jest dozwolone w metodzie procedury zwrotnej
32108=Nierozpoznany pakiet
32109=Utracono po\u0142\u0105czenie
32110=Operacja nawi\u0105zywania po\u0142\u0105czenia jest ju\u017c w toku
32111=Klient zosta\u0142 zamkni\u0119ty
32200=Trwa\u0142o\u015b\u0107 jest ju\u017c w u\u017cyciu
32201=Znacznik jest ju\u017c w u\u017cyciu
32202=Zbyt wiele operacji publikowania jest w toku
