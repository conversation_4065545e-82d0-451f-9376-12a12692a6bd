#/* 
# * Copyright (c) 2009, 2012 IBM Corp.
# *
# * All rights reserved. This program and the accompanying materials
# * are made available under the terms of the Eclipse Public License v1.0
# * which accompanies this distribution, and is available at
# * http://www.eclipse.org/legal/epl-v10.html
# *
# * Contributors:
# *    Dave <PERSON> - initial API and implementation and/or initial documentation
# */
# NLS_MESSAGEFORMAT_VAR
# NLS_ENCODING=UNICODE
1=Versione di protocollo non valida
2=ID client non valido
3=Broker non disponibile
4=Nome utente o password non validi
5=Non autorizzato per la connessione
6=Errore imprevisto
32000=Scaduto in attesa di una risposta dal server
32100=Client connesso
32101=Client disconnesso
32102=Client in fase di disconnessione
32103=Impossibile effettuare la connessione al server
32104=Client non connesso
32105=Il tipo SocketFactory specificato non corrisponde all'URI del broker
32106=Errore di configurazione SSL
32107=Disconnessione non consentita da un metodo callback
32108=Pacchetto non riconosciuto
32109=Connessione persa
32110=Connessione gi\u00e0 in corso
32111=Client chiuso
32200=Persistenza gi\u00e0 in uso
32201=Token gi\u00e0 in uso
32202=Numero eccessivo di pubblicazioni in corso
