0=MQTT Catalog
101=<init> ClientID={0} ServerURI={1} PersistenceType={2}
103=cleanSession={0} connectionTimeout={1} TimekeepAlive={2} userName={3} password={4} will={5} userContext={6} callback={7}
104=> quiesceTimeout={0} userContext={1} callback={2}
105=< exception
106=Subscribe topicFilter={0} userContext={1} callback={2}
107=Unsubscribe topic={0} userContext={1} callback={2}
108=<
109=<
110=<
111=< topic={0} message={1}userContext={1} callback={2}
112=<
113=<
114=>
115=URI={0}
116=URI={0}
117=>
118=<200=internalSend key={0} message={1} token={2}
119=Invalid URI Provided that could not be used to create a NetworkModule: {0}
204=connect failed: rc={0}
207=connect failed: not disconnected {0}
208=failed: not connected
209=connect failed: unexpected exception
210=failed: called on callback thread
211=failed: already disconnected
212=connect failed: unexpected exception
213=fail: token in use: key={0} message={1} token={2}
214=state=CONNECTING
215=state=CONNECTED
216=state=DISCONNECTING
217=state=DISCONNECTED
218=state=DISCONNECTING
219=failed: already disconnecting
220=>
221=>
222=>
223=failed: in closed state
224=failed: not disconnected
250=Failed to create TCP socket
252=connect to host {0} port {1} timeout {2}
260=setEnabledCiphers ciphers={0}
300=key={0} message={1}
301=received {0}
302=existing key={0} message={1} token={2}
303=creating new token key={0} message={1} token={2}
305=> {0} tokens
306=key={0}
307=key={0} token={1}
308=<>
309=resp={0}
310=>
311=>
312=>
400=>key={0} timeout={1} sent={2} completed={3} hasException={4} response={5} token={6}
401=failed with exception
402=key={0} response={1}
403=> key={0}
404=>key={0} response={1} excep={2}
406=key={0} timed out token={1}
407=key={0} wait max={1} token={2}
408=key={0} wait max={1}
409=wait key={0}
410=> key={0}
411=>key={0} response={1} excep={2}
500=Attempting to reconnect client: {0}
501=Automatic Reconnect Successful: {0}
502=Automatic Reconnect failed, rescheduling: {0}
503=Start reconnect timer for client: {0}, delay: {1}
504=Stop reconnect timer for client: {0}
505=Rescheduling reconnect timer for client: {0}, delay: {1}
506=Triggering Automatic Reconnect attempt.
507=Client Connected, Offline Buffer available, but not empty. Adding message to buffer. message={0}
508=Client Resting, Offline Buffer available. Adding message to buffer. message={0}
509=Client Reconnected, Offline Buffer Available. Sending Buffered Messages.
510=Publishing Buffered message message={0}
511=outbound QoS 0 publish key={0} message={1}
512=QoS 0 publish key={0}
513=Persisted Buffered Message key={0}
514=Failed to persist buffered message key={0}
515=Could not Persist, attempting to Re-Open Persistence Store
516=Restoring all buffered messages.
517=Un-Persisting Buffered message key={0}
518=Failed to Un-Persist Buffered message key={0}
519=Error occurred attempting to publish buffered message due to disconnect. Exception: {0}:{1}.
529=Sent {0}
600=>
601=key={0} message={1}
602=key={0} exception
603=clearState
604=inbound QoS 2 publish key={0} message={1}
605=outbound QoS 2 pubrel key={0} message={1}
606=outbound QoS 2 completed key={0} message={1}
607=outbound QoS 2 publish key={0} message={1}
608=outbound QoS 1 publish key={0} message={1}
609=removing orphaned pubrel key={0}
610=QoS 2 publish key={0}
611=QoS 2 pubrel key={0}
612=QoS 1 publish key={0}
613= sending {0} msgs at max inflight window
615=pending send key={0} message {1}
616=checkForActivity entered
617=+1 inflightpubrels={0}
618=key={0} QoS={1}
619=Timed out as no activity, keepAlive={0} lastOutboundActivity={1} lastInboundActivity={2} time={3} lastPing={4}
620=ping needed. keepAlive={0} lastOutboundActivity={1} lastInboundActivity={2}
621=no outstanding flows and not connected
622=inflight window full
623=+1 actualInFlight={0}
624=Schedule next ping at {0}
625=key={0}
626=quiescing={0} actualInFlight={1} pendingFlows={2} inFlightPubRels={3} callbackQuiesce={4} tokens={5}
627=received key={0} message={1}
628=pending publish key={0} qos={1} message={2}
629=received key={0} token={1} message={2}
630=received bytes count={0}
631=connected
632=reason {0}
633=disconnected
634=ping not needed yet. Schedule next ping.
635=ping sent. pingOutstanding: {0}
636=ping response received. pingOutstanding: {0}
637=timeout={0}
638=notifying queueLock holders
639=wait for outstanding: actualInFlight={0} pendingFlows={1} inFlightPubRels={2} tokens={3}
640=finished
641=remove publish from persistence. key={0}
642=Timed out as no write activity, keepAlive={0} lastOutboundActivity={1} lastInboundActivity={2} time={3} lastPing={4}
643=sent bytes count={0}
644=wait for new work or for space in the inflight window
645=removed QoS 2 publish/pubrel. key={0}, -1 inFlightPubRels={1}
646=-1 actualInFlight={0}
647=new work or ping arrived 
648=key{0}, msg={1}, excep={2}
649=key={0},excep={1}
650=removed Qos 1 publish. key={0}
651=received key={0} message={1}
659=start timer for client:{0}
660=Check schedule at {0}
661=stop
662=no message found for ack id={0}
700=stopping
701=notify workAvailable and wait for run
703=stopped
704=wait for workAvailable
705=callback and notify for key={0}
706=notify spaceAvailable
708=call connectionLost
709=wait for spaceAvailable
710=new msg avail, notify workAvailable
711=quiesce notify spaceAvailable
713=call messageArrived key={0} topic={1}
714=callback threw exception
715=new workAvailable. key={0}
716=call onSuccess key={0}
717=call onFailure key {0}
719=callback threw ex:
720=exception from connectionLost {0}
800=stopping sender
801=stopped
802=network send key={0} msg={1}
803=get message returned null, stopping}
804=exception
805=<
850=stopping
851=stopped
852=network read message
853=Stopping due to IOException
854=<
855=starting
856=Stopping, MQttException
857=Unknown PubAck, PubComp or PubRec received. Ignoring.
