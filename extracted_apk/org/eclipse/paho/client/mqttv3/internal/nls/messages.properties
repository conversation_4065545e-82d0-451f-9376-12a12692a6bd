#/* 
# * Copyright (c) 2009, 2012 IBM Corp.
# *
# * All rights reserved. This program and the accompanying materials
# * are made available under the terms of the Eclipse Public License v1.0
# * which accompanies this distribution, and is available at
# * http://www.eclipse.org/legal/epl-v10.html
# *
# * Contributors:
# *    Dave Locke - initial API and implementation and/or initial documentation
# */
# NLS_MESSAGEFORMAT_VAR
# NLS_ENCODING=UNICODE
1=Invalid protocol version
2=Invalid client ID
3=Broker unavailable
4=Bad user name or password
5=Not authorized to connect
6=Unexpected error
119=Invalid URI Provided that could not be used to create a NetworkModule: {0}
32000=Timed out waiting for a response from the server
32001=Internal error, caused by no new message IDs being available
32002=Timed out while waiting to write messages to the server
32100=Client is connected
32101=Client is disconnected
32102=Client is currently disconnecting
32103=Unable to connect to server
32104=Client is not connected
32105=The specified SocketFactory type does not match the broker URI
32106=SSL configuration error
32107=Disconnecting is not allowed from a callback method
32108=Unrecognized packet
32109=Connection lost
32110=Connect already in progress
32111=Client is closed
32200=Persistence already in use
32201=Token already in use
32202=Too many publishes in progress
