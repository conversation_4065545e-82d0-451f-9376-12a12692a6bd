#/* 
# * Copyright (c) 2009, 2012 IBM Corp.
# *
# * All rights reserved. This program and the accompanying materials
# * are made available under the terms of the Eclipse Public License v1.0
# * which accompanies this distribution, and is available at
# * http://www.eclipse.org/legal/epl-v10.html
# *
# * Contributors:
# *    Dave Locke - initial API and implementation and/or initial documentation
# */
# NLS_MESSAGEFORMAT_VAR
# NLS_ENCODING=UNICODE
1=Versi\u00f3n de protocolo incorrecta
2=Identificador de cliente incorrecto
3=Intermediario no disponible
4=Nombre de usuario o contrase\u00f1a incorrecto
5=No autorizado a conectarse
6=Error inesperado
32000=Tiempo de espera excedido al esperar una respuesta del servidor
32100=El cliente est\u00e1 conectado
32101=El cliente est\u00e1 desconectado
32102=El cliente se est\u00e1 desconectando
32103=No es posible conectarse al servidor
32104=El cliente no est\u00e1 conectado
32105=El tipo SocketFactory especificado no coincide con el URI del intermediario
32106=Error de configuraci\u00f3n SSL
32107=No se permite la desconexi\u00f3n desde un m\u00e9todo de devoluci\u00f3n de llamada
32108=Paquete no reconocido
32109=Se ha perdido la conexi\u00f3n
32110=Conexi\u00f3n ya en curso
32111=El cliente est\u00e1 cerrado
32200=La persistencia ya se est\u00e1 utilizando
32201=La se\u00f1al ya se est\u00e1 utilizando
32202=Demasiadas publicaciones en curso
