{
  "carConfig": [
    {
      "platform": "AVNTG2A39",       //广汽A39
      "channelid":"GUANGQI_A39",
      "channelname": "GAC",
      "audioSource": "-1",            //AudioRecord audioSource值定义， -1代表不需要定制， 用默认的即可
      "voipAudioSource": "-1",        //VOIPAudioRecord audioSource值定义， -1代表不需要定制， 用默认的即可
      "voiceChannelConfig": "1",      //回复微信场景下录音声道选择， 0: both, 1: left, 2: right
      "messageStreamType":"11",       //微信消息播报音源类型
      "voipStreamType":"15",          //微信voip电话音源类型
      "notificationStreamType":"16",  //微信提示音音源类型
      "voipRingStreamType":"16",      //微信voip铃声
      "windowpadding": "0,0,0,0",     //窗口left, top, right, bottom边距
      "windowtype": "-1",             //浮窗类型
      "audiotrackwithnewusage": "0",  //是否使用新的方式构造AudioTrack, 0:否， 1：是
      "canSetAudioModeWhenVoip": true,
      "showUserGuide": false,
      "smartScreenAdapt": "",
      "windowsizeoffset": "0,0,0,0",
      "speechMsgDelay": "0",           //语音消息延时播报
      "isUseSystemBtBroadcast": true,
      "isSupportNotDisturb": false     //是否支持免打扰模式
    },
    {
      "platform": "S311R",    //长安S311R
      "channelid":"CHANGAN_S311R",
      "channelname": "",
      "audioSource": "1",
      "voipAudioSource": "1",
      "voiceChannelConfig": "1",
      "messageStreamType":"4",
      "voipStreamType":"0",
      "notificationStreamType":"3",
      "voipRingStreamType":"3",
      "windowpadding": "0,0,0,0",
      "windowtype": "-1",
      "audiotrackwithnewusage": "0",
      "canSetAudioModeWhenVoip": true,
      "showUserGuide": false,
      "smartScreenAdapt": "",
      "windowsizeoffset": "0,0,0,0"
    },
    {
      "platform": "IHU05",                //长城F5  2057
      "channelid":"CHANGCHENG_F5",
      "channelname": "",
      "audioSource": "-1",
      "voipAudioSource": "-1",
      "voiceChannelConfig": "1",
      "messageStreamType":"20",
      "voipStreamType":"17",
      "notificationStreamType":"26",
      "voipRingStreamType":"26",
      "windowpadding": "0,0,0,20",
      "windowtype": "-1",
      "audiotrackwithnewusage": "0",
      "canSetAudioModeWhenVoip": true,
      "showUserGuide": false,
      "smartScreenAdapt": "",
      "isModifySystemFont": true,
      "windowsizeoffset": "0,0,0,0"
    },
    {
      "platform": "CQ9056",               //长城F7
      "channelid":"CHANGCHENG_F7",
      "channelname": "",
      "audioSource": "-1",
      "voipAudioSource": "-1",
      "voiceChannelConfig": "1",
      "messageStreamType":"20",
      "voipStreamType":"17",
      "notificationStreamType":"26",
      "voipRingStreamType":"26",
      "windowpadding": "0,0,0,20",
      "windowtype": "-1",
      "audiotrackwithnewusage": "0",
      "canSetAudioModeWhenVoip": true,
      "showUserGuide": false,
      "smartScreenAdapt": "",
      "isModifySystemFont": true,
      "windowsizeoffset": "0,0,0,0"
    },
    {
      "platform": "Motors-CQ9056",               //长城F7-21
      "channelid":"CHANGCHENG_F7_21",
      "channelname": "",
      "audioSource": "1",
      "voipAudioSource": "1",
      "voiceChannelConfig": "1",
      "messageStreamType":"12",
      "voipStreamType":"0",
      "notificationStreamType":"9",
      "voipRingStreamType":"2",
      "windowpadding": "0,0,0,0",
      "windowtype": "2057",
      "audiotrackwithnewusage": "0",
      "canSetAudioModeWhenVoip": false,
      "showUserGuide": true,
      "smartScreenAdapt": "",
      "windowsizeoffset": "464,0,0,0",
      "returnMicFocusDelay": true,
      "instructionWeChatKeyAttributes": "1,0",
      "doaEnable": true,
      "isModifySystemFont": true,
      "isIgnoreVuiResult": false
    },
    {
      "platform": "RUILIAN",
      "channelid":"RUILIAN",
      "channelname": "",
      "audioSource": "-1",
      "voipAudioSource": "-1",
      "voiceChannelConfig": "0",
      "messageStreamType":"3",
      "voipStreamType":"3",
      "notificationStreamType":"3",
      "voipRingStreamType":"3",
      "windowpadding": "0,0,0,0",
      "windowtype": "-1",
      "audiotrackwithnewusage": "0",
      "canSetAudioModeWhenVoip": true,
      "showUserGuide": false,
      "smartScreenAdapt": "",
      "windowsizeoffset": "0,0,0,0"
    },
    {
      "platform": "G6SA-r8a7796",          //A86
      "channelid":"GUANGQI_A86",
      "channelname": "GAC",
      "audioSource": "1",
      "voipAudioSource": "1",
      "voiceChannelConfig": "1",
      "messageStreamType":"9",
      "voipStreamType":"0",
      "notificationStreamType":"5",
      "voipRingStreamType":"5",
      "windowpadding": "0,0,0,0",
      "windowtype": "-1",
      "audiotrackwithnewusage": "0",
      "canSetAudioModeWhenVoip": true,
      "showUserGuide": true,
      "smartScreenAdapt": "",
      "windowsizeoffset": "0,0,0,0"
    },
    {
      "platform": "IHU03",                //T5
      "channelid":"LIUQI_T5",
      "channelname": "",
      "audioSource": "-1",
      "voipAudioSource": "-1",
      "voiceChannelConfig": "1",
      "messageStreamType":"20",
      "voipStreamType":"17",
      "notificationStreamType":"26",
      "voipRingStreamType":"26",
      "windowpadding": "0,0,0,0",
      "windowtype": "-1",
      "audiotrackwithnewusage": "0",
      "canSetAudioModeWhenVoip": true,
      "showUserGuide": false,
      "smartScreenAdapt": "",
      "windowsizeoffset": "0,0,0,0"
    },
    {
      "platform": "S202",                //S202H
      "channelid":"CHANGAN_S202H",
      "channelname": "",
      "audioSource": "1",
      "voipAudioSource": "1",
      "voiceChannelConfig": "1",
      "messageStreamType":"4",
      "voipStreamType":"0",
      "notificationStreamType":"3",
      "voipRingStreamType":"3",
      "windowpadding": "0,0,0,0",
      "windowtype": "-1",
      "audiotrackwithnewusage": "0",
      "canSetAudioModeWhenVoip": true,
      "showUserGuide": false,
      "smartScreenAdapt": "",
      "windowsizeoffset": "0,0,0,0"
    },
    {
      "platform": "S202M",                //S202M
      "channelid":"CHANGAN_S202M",
      "channelname": "",
      "audioSource": "1",
      "voipAudioSource": "1",
      "voiceChannelConfig": "1",
      "messageStreamType":"4",
      "voipStreamType":"0",
      "notificationStreamType":"3",
      "voipRingStreamType":"3",
      "windowpadding": "0,0,0,0",
      "windowtype": "-1",
      "audiotrackwithnewusage": "0",
      "canSetAudioModeWhenVoip": true,
      "showUserGuide": false,
      "smartScreenAdapt": "",
      "windowsizeoffset": "0,0,0,0"
    },
    {
      "platform": "S203EV",                //S203ev
      "channelid":"CHANGAN_S203EV",
      "channelname": "",
      "audioSource": "1",
      "voipAudioSource": "1",
      "voiceChannelConfig": "1",
      "messageStreamType":"4",
      "voipStreamType":"0",
      "notificationStreamType":"3",
      "voipRingStreamType":"3",
      "windowpadding": "0,0,0,0",
      "windowtype": "-1",
      "audiotrackwithnewusage": "0",
      "canSetAudioModeWhenVoip": true,
      "showUserGuide": false,
      "smartScreenAdapt": "",
      "windowsizeoffset": "0,0,0,0"
    },
    {
      "platform": "C211EV",                //C211EV
      "channelid":"CHANGAN_C211EV",
      "channelname": "",
      "audioSource": "1",
      "voipAudioSource": "1",
      "voiceChannelConfig": "1",
      "messageStreamType":"4",
      "voipStreamType":"0",
      "notificationStreamType":"3",
      "voipRingStreamType":"3",
      "windowpadding": "0,0,0,0",
      "windowtype": "-1",
      "audiotrackwithnewusage": "0",
      "canSetAudioModeWhenVoip": true,
      "showUserGuide": false,
      "smartScreenAdapt": "",
      "windowsizeoffset": "0,0,0,0"
    },
    {
      "platform": "C211EVL",                //C211EVL
      "channelid":"CHANGAN_C211EVL",
      "channelname": "",
      "audioSource": "1",
      "voipAudioSource": "1",
      "voiceChannelConfig": "1",
      "messageStreamType":"4",
      "voipStreamType":"0",
      "notificationStreamType":"3",
      "voipRingStreamType":"3",
      "windowpadding": "0,0,0,0",
      "windowtype": "-1",
      "audiotrackwithnewusage": "0",
      "canSetAudioModeWhenVoip": true,
      "showUserGuide": false,
      "smartScreenAdapt": "",
      "windowsizeoffset": "0,0,0,0"
    },
    {
      "platform": "NL-3BG",                //吉利NL-3BG
      "channelid":"JILI_NL-3BG",
      "channelname": "",
      "audioSource": "1",
      "voipAudioSource": "1",
      "voiceChannelConfig": "0",
      "messageStreamType":"9",
      "voipStreamType":"0",
      "notificationStreamType":"3",
      "voipRingStreamType":"3",
      "windowpadding": "0,0,0,0",
      "windowtype": "2057",
      "audiotrackwithnewusage": "1",
      "canSetAudioModeWhenVoip": true,
      "showUserGuide": false,
      "smartScreenAdapt": "",
      "isStopLoginWakeup": true,
      "windowsizeoffset": "0,0,0,0"
    },
    {
      "platform": "ecarxICp1_64",          //吉利NL-3BG android9.0
      "channelid":"JILI_NL-3BG",
      "channelname": "",
      "audioSource": "1",
      "voipAudioSource": "1",
      "voiceChannelConfig": "1",
      "messageStreamType":"30",
      "voipStreamType":"10",
      "notificationStreamType":"80",
      "voipRingStreamType":"80",
      "windowpadding": "0,0,0,0",
      "windowtype": "-1",
      "audiotrackwithnewusage": "0",
      "canSetAudioModeWhenVoip": true,
      "showUserGuide": false,
      "smartScreenAdapt": "",
      "windowsizeoffset": "0,0,0,0"
    },
    {
      "platform": "aed for arm64",                //奥迪aed
      "channelid":"AODI_AED",
      "channelname": "",
      "audioSource": "1",
      "voipAudioSource": "1",
      "voiceChannelConfig": "0",
      "messageStreamType":"3",
      "voipStreamType":"5",
      "notificationStreamType":"1",
      "voipRingStreamType":"1",
      "windowpadding": "0,0,0,0",
      "windowtype": "2037",
      "audiotrackwithnewusage": "1",
      "canSetAudioModeWhenVoip": true,
      "showUserGuide": false,
      "smartScreenAdapt": "",
      "windowsizeoffset": "0,0,0,0",
      "returnMicFocusDelay": true
    },
    {
      "platform": "G6SA-r8a7796",                //江铃福特G6SA
      "channelid":"JIANGLINGFUTE_G6SA",
      "channelname": "",
      "audioSource": "1",
      "voipAudioSource": "1",
      "voiceChannelConfig": "1",
      "messageStreamType":"9",
      "voipStreamType":"0",
      "notificationStreamType":"5",
      "voipRingStreamType":"5",
      "windowpadding": "0,0,0,0",
      "windowtype": "-1",
      "audiotrackwithnewusage": "0",
      "canSetAudioModeWhenVoip": true,
      "showUserGuide": false,
      "smartScreenAdapt": "",
      "windowsizeoffset": "0,0,0,0"
    },
    {
      "platform": "LEADING_IDEAL One",                //车和家LEADING_IDEAL One
      "channelid":"CHEHEJIA_LEADING_IDEAL_One",
      "channelname": "",
      "audioSource": "7",
      "voipAudioSource": "7",
      "voiceChannelConfig": "0",
      "messageStreamType":"9",
      "voipStreamType":"0",
      "notificationStreamType":"5",
      "voipRingStreamType":"5",
      "windowpadding": "0,0,0,0",
      "windowtype": "-1",
      "audiotrackwithnewusage": "1",
      "canSetAudioModeWhenVoip": false,
      "showUserGuide": false,
      "smartScreenAdapt": "",
      "windowsizeoffset": "688,0,0,0",
      "isSpeechForField": true,
      "isPalRequestMic": false,  //是否向PAL申请MIC
      "doaEnable": true,
      "isShieldHyperlinks": false
    },
    {
      "platform": "SABRESD-MX6DQ",                //广丰819
      "channelid":"GUANGFENG_819",
      "channelname": "",
      "audioSource": "1",
      "voipAudioSource": "1",
      "voiceChannelConfig": "0",
      "messageStreamType":"403",
      "voipStreamType":"402",
      "notificationStreamType":"403",
      "voipRingStreamType":"409",
      "windowpadding": "0,0,0,0",
      "windowtype": "2500",
      "audiotrackwithnewusage": "0",
      "canSetAudioModeWhenVoip": true,
      "showUserGuide": false,
      "smartScreenAdapt": "",
      "windowsizeoffset": "0,0,0,0",
      "isIgnoreVuiResult": false,
      "instructionWeChatKeyAttributes": "1,2",
      "isPalRequestMic": true  //是否向PAL申请MIC
    },
    {
      "platform": "CNS3",                //大众poc
      "channelid":"DAZHONG_CNS3",
      "channelname": "",
      "audioSource": "1",
      "voipAudioSource": "1",
      "voiceChannelConfig": "0",
      "messageStreamType":"3",
      "voipStreamType":"3",
      "notificationStreamType":"3",
      "voipRingStreamType":"3",
      "windowpadding": "0,0,0,0",
      "windowtype": "-1",
      "audiotrackwithnewusage": "0",
      "canSetAudioModeWhenVoip": true,
      "showUserGuide": false,
      "smartScreenAdapt": "",
      "windowsizeoffset": "0,0,0,0"
    },
    {
      "platform": "S302_ICA",                //S302_ICA
      "channelid":"CHANGAN_S302_ICA",
      "channelname": "",
      "audioSource": "1",
      "voipAudioSource": "1",
      "voiceChannelConfig": "1",
      "messageStreamType":"4",
      "voipStreamType":"0",
      "notificationStreamType":"3",
      "voipRingStreamType":"3",
      "windowpadding": "0,0,0,0",
      "windowtype": "-1",
      "audiotrackwithnewusage": "0",
      "canSetAudioModeWhenVoip": true,
      "showUserGuide": false,
      "smartScreenAdapt": "",
      "windowsizeoffset": "0,0,0,0"
    },
    {
      "platform": "S111_MCA",                //S111_MCA
      "channelid":"CHANGAN_S111_MCA",
      "channelname": "",
      "audioSource": "1",
      "voipAudioSource": "1",
      "voiceChannelConfig": "1",
      "messageStreamType":"4",
      "voipStreamType":"0",
      "notificationStreamType":"3",
      "voipRingStreamType":"3",
      "windowpadding": "0,0,0,0",
      "windowtype": "-1",
      "audiotrackwithnewusage": "0",
      "canSetAudioModeWhenVoip": true,
      "showUserGuide": false,
      "smartScreenAdapt": "",
      "windowsizeoffset": "0,0,0,0"
    },
    {
      "platform": "X70",                //奇瑞X70
      "channelid":"CHANGAN_X70",
      "channelname": "",
      "audioSource": "1",
      "voipAudioSource": "1",
      "voiceChannelConfig": "1",
      "messageStreamType":"4",
      "voipStreamType":"0",
      "notificationStreamType":"3",
      "voipRingStreamType":"3",
      "windowpadding": "0,0,0,0",
      "windowtype": "-1",
      "audiotrackwithnewusage": "0",
      "canSetAudioModeWhenVoip": true,
      "showUserGuide": false,
      "smartScreenAdapt": "",
      "windowsizeoffset": "0,0,0,0"
    },
    {
      "platform": "BAH2-AL10",       //华为Pad
      "channelid":"BAH2-AL10",
      "channelname": "",
      "audioSource": "1",
      "voipAudioSource": "1",
      "voiceChannelConfig": "0",
      "messageStreamType":"3",
      "voipStreamType":"3",
      "notificationStreamType":"3",
      "voipRingStreamType":"3",
      "windowpadding": "0,0,0,0",
      "windowtype": "-1",
      "audiotrackwithnewusage": "1",
      "canSetAudioModeWhenVoip": true,
      "showUserGuide": false,
      "smartScreenAdapt": "360,640,720,0.5",     //第一个值是长横屏(w/h>2)短边设计基准，第二个值短横屏长边基准，第三个竖屏宽度，第四个缩放比例
      "windowsizeoffset": "0,0,0,0"
    },
    {
      "platform": "GWM-V3-r8a7795",               //长城H(B0106)
      "channelid":"CHANGCHENG_H_B0106",
      "channelname": "",
      "audioSource": "1",
      "voipAudioSource": "1",
      "voiceChannelConfig": "0",
      "messageStreamType":"12",
      "voipStreamType":"13",
      "notificationStreamType":"4",
      "voipRingStreamType":"3",
      "windowpadding": "0,0,0,0",
      "windowtype": "-1",
      "audiotrackwithnewusage": "1",
      "canSetAudioModeWhenVoip": false,
      "showUserGuide": true,
      "smartScreenAdapt": "",
      "windowsizeoffset": "0,0,0,0",
      "returnMicFocusDelay": true,
      "instructionWeChatKeyAttributes": "0,1",
      "isPalRequestMic": true,
      "speechMsgDelay": "800",
      "isModifySystemFont": true,
      "isIgnoreVuiResult": false
    },
    {
      "platform": "Salvator-X-r8a7796",         //长城A02
      "channelid": "CHANGCHENG_A02",
      "channelname": "",
      "audioSource": "1",
      "voipAudioSource": "1",
      "voiceChannelConfig": "1",
      "messageStreamType": "9",
      "voipStreamType": "14",
      "notificationStreamType": "9",
      "voipRingStreamType": "14",
      "windowpadding": "0,0,0,0",
      "windowtype": "2057",
      "audiotrackwithnewusage": "1",
      "canSetAudioModeWhenVoip": false,
      "showUserGuide": true,
      "smartScreenAdapt": "",
      "windowsizeoffset": "0,0,0,0",
      "returnMicFocusDelay": true,
      "doaEnable": true,
      "isModifySystemFont": true,
      "instructionWeChatKeyAttributes": "1,0"
    },
    {
      "platform": "IHU08",                //柳汽SX5G2021
      "channelid":"LIUQI_SX5G2021",
      "channelname": "",
      "audioSource": "1",
      "voipAudioSource": "1",
      "voiceChannelConfig": "1",
      "messageStreamType":"20",
      "voipStreamType":"17",
      "notificationStreamType":"26",
      "voipRingStreamType":"26",
      "windowpadding": "0,0,0,0",
      "windowtype": "2101",
      "audiotrackwithnewusage": "0",
      "canSetAudioModeWhenVoip": true,
      "showUserGuide": true,
      "smartScreenAdapt": "",
      "windowsizeoffset": "0,0,0,0",
      "doaEnable": false,
      "returnMicFocusDelay": true
    },
    {
      "platform": "CX756-NV2050",          //江铃福特CX756
      "channelid":"JIANGLINGFUTE_CX756",
      "channelname": "JIANGLING",
      "audioSource": "1",
      "voipAudioSource": "1",
      "voiceChannelConfig": "1",
      "messageStreamType":"9",
      "voipStreamType":"0",
      "notificationStreamType":"9",
      "voipRingStreamType":"2",
      "windowpadding": "0,0,0,0",
      "windowtype": "-1",
      "audiotrackwithnewusage": "1",
      "canSetAudioModeWhenVoip": true,
      "showUserGuide": true,
      "smartScreenAdapt": "",
      "windowsizeoffset": "0,0,0,0",
      "instructionWeChatKeyAttributes": "0,0",  //配置方向盘微信键属性（右边，微信icon）
      "isIgnoreVuiResult": false
    },
    {
      "platform": "CX743MCA",          //江铃福特CX743
      "channelid":"JIANGLINGFUTE_CX743MCA",
      "channelname": "JIANGLING",
      "audioSource": "1",
      "voipAudioSource": "1",
      "voiceChannelConfig": "1",
      "messageStreamType":"9",
      "voipStreamType":"0",
      "notificationStreamType":"9",
      "voipRingStreamType":"2",
      "windowpadding": "0,0,0,0",
      "windowtype": "-1",
      "audiotrackwithnewusage": "1",
      "canSetAudioModeWhenVoip": true,
      "showUserGuide": true,
      "smartScreenAdapt": "",
      "windowsizeoffset": "0,0,0,0",
      "instructionWeChatKeyAttributes": "1,0",  //配置方向盘微信键属性（右边，微信icon）
      "isIgnoreVuiResult": false,
      "isHideStatusBar": true,
      "doaEnable": true
    },
    {
      "platform": "CX756-NV2052",          //江铃福特CX756新增tai4.0车型
      "channelid":"CX756-NV2052",
      "channelname": "CX756-NV2052",
      "audioSource": "1",
      "voipAudioSource": "1",
      "voiceChannelConfig": "1",
      "messageStreamType":"9",
      "voipStreamType":"0",
      "notificationStreamType":"9",
      "voipRingStreamType":"2",
      "windowpadding": "0,0,0,0",
      "windowtype": "-1",
      "audiotrackwithnewusage": "1",
      "canSetAudioModeWhenVoip": true,
      "showUserGuide": true,
      "smartScreenAdapt": "",
      "windowsizeoffset": "0,0,0,0",
      "instructionWeChatKeyAttributes": "1,0",  //配置方向盘微信键属性（右边，微信icon）
      "isIgnoreVuiResult": false,
      "isHideStatusBar": true,
      "doaEnable": true
    },
    {
      "platform": "msmnile_gvmq for arm64",  //广汽A55
      "channelid": "GUANGQI_A55",
      "channelname": "",
      "audioSource": "1",
      "voipAudioSource": "1",
      "voiceChannelConfig": "1",
      "messageStreamType": "26",
      "voipStreamType": "20",
      "notificationStreamType": "27",
      "voipRingStreamType": "21",
      "windowpadding": "0,0,0,0",
      "windowtype": "-1",
      "audiotrackwithnewusage": "1",
      "canSetAudioModeWhenVoip": true,
      "showUserGuide": true,
      "smartScreenAdapt": "",
      "instructionWeChatKeyAttributes": "1,0",  //配置方向盘微信键属性（左边，微信icon）
      "isSupportDashboard": false //是否支持仪表显示
    },
    {
      "platform": "WT-3.0R-POC", // 梧桐内部主线3.0
      "channelid": "WT-3.0R-POC",
      "channelname": "",
      "audioSource": "1",
      "voipAudioSource": "1",
      "voiceChannelConfig": "1",
      "messageStreamType": "9",
      "voipStreamType": "0",
      "notificationStreamType": "5",
      "voipRingStreamType": "2",
      "windowpadding": "0,0,0,0",
      "windowtype": "-1",
      "audiotrackwithnewusage": "0",
      "canSetAudioModeWhenVoip": false,
      "showUserGuide": true,
      "smartScreenAdapt": ""
    },
    {
      "platform": "WT30R_M8666", // 梧桐WT30R_M8666
      "channelid": "WT30R_M8666",
      "channelname": "",
      "audioSource": "1",
      "voipAudioSource": "1",
      "voiceChannelConfig": "1",
      "messageStreamType": "9",
      "voipStreamType": "0",
      "notificationStreamType": "5",
      "voipRingStreamType": "0",
      "windowpadding": "0,0,0,0",
      "windowtype": "-1",
      "audiotrackwithnewusage": "0",
      "canSetAudioModeWhenVoip": false,
      "showUserGuide": true,
      "smartScreenAdapt": ""
    },
    {
      "platform": "msmnile_gvmq for arm64", // 广汽A60
      "channelid": "GUANGQI_A60",
      "channelname": "",
      "audioSource": "1",
      "voipAudioSource": "1",
      "voiceChannelConfig": "0",
      "messageStreamType": "10000001",
      "voipStreamType": "2",
      "notificationStreamType": "16",
      "voipRingStreamType": "6",
      "windowpadding": "0,0,0,0",
      "windowtype": "-1",
      "audiotrackwithnewusage": "1",
      "canSetAudioModeWhenVoip": true,
      "showUserGuide": true,
      "smartScreenAdapt": "",
      "instructionWeChatKeyAttributes": "1,0"  //配置方向盘微信键属性（左边，微信icon）
    },
    {
      "platform": "S811-Salvator-X-r8a7796", //江淮S811
      "channelid": "S811-Salvator-X-r8a7796",
      "channelname": "",
      "audioSource": "1",
      "voipAudioSource": "1",
      "voiceChannelConfig": "1",
      "messageStreamType": "9",
      "voipStreamType": "6",
      "notificationStreamType": "9",
      "voipRingStreamType": "1000",
      "windowpadding": "0,0,0,0",
      "windowtype": "2038",
      "audiotrackwithnewusage": "1",
      "canSetAudioModeWhenVoip": true,
      "showUserGuide": true,
      "smartScreenAdapt": "",
      "voipReleaseStreamType": "6",
      "isHideNavBar": true
    },
    {
      "platform": "sv51c1_m",   //上汽大通
      "productid": "sv51c1_m",
      "channelid": "sv51c1_m",
      "channelname": "",
      "audioSource": "1",
      "voipAudioSource": "1",
      "voiceChannelConfig": "0",
      "messageStreamType": "17",
      "voipStreamType": "38",
      "notificationStreamType": "5",
      "voipRingStreamType": "6",
      "windowpadding": "0,0,0,0",
      "windowtype": "-1",
      "smartScreenAdapt": "",
      "audiotrackwithnewusage": "1",
      "canSetAudioModeWhenVoip": true,
      "showUserGuide": true,
      "palBTCall":true,
      "returnMicFocusDelay": true,
      "isPalRequestMic": true,
      "isSpeechForField": true,
      "isStopLoginWakeup": false,
      "instructionWeChatKeyAttributes": "0,1"  //配置方向盘微信键属性（右边，星号icon）
    },
    {
      "platform": "sk83b2d6",  //上汽大通6125平台车型，此平台车型上14版本微信
      "channelid": "sk83b2d6",
      "channelname": "",
      "audioSource": "1",
      "voiceChannelConfig": "0",
      "messageStreamType": "3",
      "voipStreamType": "3",
      "notificationStreamType": "3",
      "voipRingStreamType": "3",
      "windowpadding": "0,0,0,0",
      "windowtype": "-1",
      "audiotrackwithnewusage": "1",
      "canSetAudioModeWhenVoip": true,
      "showUserGuide": true,
      "smartScreenAdapt": "",
      "palBTCall":true,
      "isIgnoreVuiResult": false,
      "returnMicFocusDelay": true,
      "isPalRequestMic": true,
      "isSpeechForField": true,
      "isUseSystemBtBroadcast":true,
      "isStopLoginWakeup": false,
      "instructionWeChatKeyAttributes": "0,1"  //配置方向盘微信键属性（右边，星号icon）
    },
    {
      "platform": "sv63",   //上汽大通
      "productid": "sv63",
      "channelid": "sv63",
      "channelname": "",
      "audioSource": "1",
      "voipAudioSource": "1",
      "voiceChannelConfig": "0",
      "messageStreamType": "17",
      "voipStreamType": "38",
      "notificationStreamType": "5",
      "voipRingStreamType": "6",
      "windowpadding": "0,0,0,0",
      "windowtype": "-1",
      "smartScreenAdapt": "",
      "audiotrackwithnewusage": "1",
      "canSetAudioModeWhenVoip": true,
      "showUserGuide": true,
      "palBTCall":true,
      "returnMicFocusDelay": true,
      "isPalRequestMic": true,
      "isSpeechForField": true,
      "isStopLoginWakeup": false,
      "instructionWeChatKeyAttributes": "0,1"  //配置方向盘微信键属性（右边，星号icon）
    },
    {
      "platform": "rv63_tai4",   //上汽大通 tai4.0演示车型
      "productid": "rv63_tai4",
      "channelid": "rv63_tai4",
      "channelname": "",
      "audioSource": "1",
      "voipAudioSource": "1",
      "voiceChannelConfig": "0",
      "messageStreamType": "17",
      "voipStreamType": "38",
      "notificationStreamType": "5",
      "voipRingStreamType": "6",
      "windowpadding": "0,0,0,0",
      "windowtype": "-1",
      "smartScreenAdapt": "",
      "audiotrackwithnewusage": "1",
      "canSetAudioModeWhenVoip": true,
      "showUserGuide": true,
      "palBTCall":true,
      "returnMicFocusDelay": true,
      "isPalRequestMic": true,
      "isSpeechForField": true,
      "isStopLoginWakeup": false,
      "instructionWeChatKeyAttributes": "0,1"  //配置方向盘微信键属性（右边，星号icon）
    },
    {
      "platform": "hy_2021y_01_h",  //上汽大通  红岩
      "productid": "hy_2021y_01_h",
      "channelid": "hy_2021y_01_h",
      "channelname": "",
      "audioSource": "1",
      "voipAudioSource": "1",
      "voiceChannelConfig": "0",
      "messageStreamType": "17",
      "voipStreamType": "38",
      "notificationStreamType": "5",
      "voipRingStreamType": "6",
      "windowpadding": "0,0,0,0",
      "windowtype": "-1",
      "smartScreenAdapt": "",
      "audiotrackwithnewusage": "1",
      "canSetAudioModeWhenVoip": true,
      "showUserGuide": true,
      "isPalRequestMic": true,
      "palBTCall":true,
      "returnMicFocusDelay": true,
      "isSpeechForField": true,
      "isStopLoginWakeup": false,
      "instructionWeChatKeyAttributes": "0,0"
    },
    {
      "platform": "rv63",   //上汽大通 RV63
      "productid": "rv63",
      "channelid": "rv63",
      "channelname": "",
      "audioSource": "1",
      "voipAudioSource": "1",
      "voiceChannelConfig": "0",
      "messageStreamType": "17",
      "voipStreamType": "38",
      "notificationStreamType": "5",
      "voipRingStreamType": "6",
      "windowpadding": "0,0,0,0",
      "windowtype": "-1",
      "smartScreenAdapt": "",
      "audiotrackwithnewusage": "1",
      "canSetAudioModeWhenVoip": true,
      "showUserGuide": true,
      "isPalRequestMic": true,
      "returnMicFocusDelay": true,
      "palBTCall":true,
      "isSpeechForField": true,
      "isStopLoginWakeup": false,
      "instructionWeChatKeyAttributes": "0,1"  //配置方向盘微信键属性（右边，微信icon）
    },
    {
      "platform": "sv91d1_net",  //大通sv91d1_net
      "productid": "sv91d1_net",
      "channelid":"sv91d1_net",
      "channelname": "",
      "audioSource": "1",
      "voipAudioSource": "1",
      "voiceChannelConfig": "0",
      "messageStreamType": "17",
      "voipStreamType": "38",
      "notificationStreamType": "5",
      "voipRingStreamType": "6",
      "windowpadding": "0,0,0,0",
      "windowtype": "-1",
      "smartScreenAdapt": "",
      "audiotrackwithnewusage": "1",
      "canSetAudioModeWhenVoip": true,
      "showUserGuide": true,
      "isPalRequestMic": true,
      "returnMicFocusDelay": true,
      "palBTCall":true,
      "isSpeechForField": true,
      "isStopLoginWakeup": false,
      "instructionWeChatKeyAttributes": "0,1"  //配置方向盘微信键属性（右边，星号icon）
    },
    {
      "platform": "sv91d2_net",  //大通sv91d2_net
      "productid": "sv91d2_net",
      "channelid":"sv91d2_net",
      "channelname": "",
      "audioSource": "1",
      "voipAudioSource": "1",
      "voiceChannelConfig": "0",
      "messageStreamType": "17",
      "voipStreamType": "38",
      "notificationStreamType": "5",
      "voipRingStreamType": "6",
      "windowpadding": "0,0,0,0",
      "windowtype": "-1",
      "smartScreenAdapt": "",
      "audiotrackwithnewusage": "1",
      "canSetAudioModeWhenVoip": true,
      "showUserGuide": true,
      "isPalRequestMic": true,
      "returnMicFocusDelay": true,
      "palBTCall":true,
      "isSpeechForField": true,
      "isStopLoginWakeup": false,
      "instructionWeChatKeyAttributes": "0,1"  //配置方向盘微信键属性（右边，星号icon）
    },
    {
      "platform": "ev63_net",  //大通ev63_net
      "productid": "ev63_net",
      "channelid":"ev63_net",
      "channelname": "",
      "audioSource": "1",
      "voipAudioSource": "1",
      "voiceChannelConfig": "0",
      "messageStreamType": "17",
      "voipStreamType": "38",
      "notificationStreamType": "5",
      "voipRingStreamType": "6",
      "windowpadding": "0,0,0,0",
      "windowtype": "-1",
      "smartScreenAdapt": "",
      "audiotrackwithnewusage": "1",
      "canSetAudioModeWhenVoip": true,
      "showUserGuide": true,
      "isPalRequestMic": true,
      "returnMicFocusDelay": true,
      "palBTCall":true,
      "isSpeechForField": true,
      "isStopLoginWakeup": false,
      "instructionWeChatKeyAttributes": "0,1"  //配置方向盘微信键属性（右边，星号icon）
    },
    {
      "platform": "A57",  //广汽A57
      "channelid": "GUANGQI_A57",
      "channelname": "",
      "audioSource": "1",
      "voipAudioSource": "1",
      "voiceChannelConfig": "1",
      "messageStreamType": "30",
      "voipStreamType": "10",
      "notificationStreamType": "80",
      "voipRingStreamType": "1000",
      "windowpadding": "0,0,0,0",
      "windowtype": "-1",
      "audiotrackwithnewusage": "1",
      "canSetAudioModeWhenVoip": true,
      "showUserGuide": true,
      "smartScreenAdapt": "",
      "instructionWeChatKeyAttributes": "1,0"
    },
    {
      "platform": "S202_ICA", // 梧桐S202_ICA
      "channelid": "S202_ICA",
      "channelname": "",
      "audioSource": "1",
      "voipAudioSource": "1",
      "voiceChannelConfig": "1",
      "messageStreamType": "9",
      "voipStreamType": "0",
      "notificationStreamType": "1",
      "voipRingStreamType": "0",
      "windowpadding": "0,0,0,0",
      "windowtype": "-1",
      "audiotrackwithnewusage": "0",
      "canSetAudioModeWhenVoip": false,
      "showUserGuide": true,
      "smartScreenAdapt": "",
      "instructionWeChatKeyAttributes": "1,0",
      "returnMicFocusDelay": true
    },
    {
      "platform": "Salvator-X-r8a7795", // 北汽BJ40
      "channelid": "BEIQI_BJ40",
      "channelname": "",
      "audioSource": "1",
      "voipAudioSource": "1",
      "voiceChannelConfig": "1",
      "messageStreamType": "9",
      "voipStreamType": "14",
      "notificationStreamType": "5",
      "voipRingStreamType": "14",
      "windowpadding": "0,0,0,0",
      "windowtype": "2020",
      "audiotrackwithnewusage": "1",
      "canSetAudioModeWhenVoip": true,
      "showUserGuide": true,
      "smartScreenAdapt": "",
      "instructionWeChatKeyAttributes": "0,0",
      "heartbeatTime": 60
    },
    {
      "platform": "E02-EVB",  //吉利E02
      "productid": "CBD501G",
      "channelid": "E02-EVB",
      "channelname": "",
      "audioSource": "1",
      "voipAudioSource": "1",
      "voiceChannelConfig": "0",
      "messageStreamType": "30",
      "voipStreamType": "10",
      "notificationStreamType": "80",
      "voipRingStreamType": "1000",
      "windowpadding": "0,0,0,0",
      "windowtype": "-1",
      "smartScreenAdapt": "",
      "audiotrackwithnewusage": "1",
      "canSetAudioModeWhenVoip": true,
      "showUserGuide": true,
      "isPalRequestMic": true,
      "isSpeechForField": true,
      "isModifySystemFont": true,
      "isShieldHyperlinks": false,
      "isShowAutoLogin": true,
      "isSupportDashboard":true,
      "isHideStatusBar": true,
      "instructionWeChatKeyAttributes": "0,1"
    },
    {
      "platform": "NL-3BC",  //吉利NL-3B
      "productid": "IHU503G",
      "channelid": "NL-3BC",
      "channelname": "",
      "audioSource": "7",
      "voipAudioSource": "7",
      "voiceChannelConfig": "0",
      "messageStreamType": "30",
      "voipStreamType": "10",
      "notificationStreamType": "80",
      "voipRingStreamType": "1000",
      "windowpadding": "0,0,0,0",
      "windowtype": "-1",
      "smartScreenAdapt": "",
      "audiotrackwithnewusage": "1",
      "canSetAudioModeWhenVoip": false,
      "showUserGuide": true,
      "isPalRequestMic": true,
      "isSpeechForField": true,
      "isShieldHyperlinks": false,
      "isModifySystemFont": true,
      "isUseSystemBtBroadcast": true,
      "isShowAutoLogin": false,
      "isIgnoreVoipResult": true,
      "isIgnoreVuiResult": false,
      "isStopLoginWakeup": false,
      "isForegroundProcess": true,
      "isHideStatusBar": true,
      "instructionWeChatKeyAttributes": "1,0"
    },
    {
      "platform": "KX11",  //吉利KX11
      "productid": "KX11",
      "channelid": "KX11",
      "channelname": "",
      "audioSource": "1",
      "voipAudioSource": "1",
      "voiceChannelConfig": "0",
      "messageStreamType": "30",
      "voipStreamType": "10",
      "notificationStreamType": "80",
      "voipRingStreamType": "1000",
      "windowpadding": "0,0,0,0",
      "windowtype": "2048",
      "smartScreenAdapt": "",
      "audiotrackwithnewusage": "1",
      "canSetAudioModeWhenVoip": true,
      "showUserGuide": true,
      "isPalRequestMic": true,
      "isSpeechForField": true,
      "isModifySystemFont": true,
      "isShieldHyperlinks": false,
      "isShowAutoLogin": false,
      "isStopLoginWakeup": false,
      "isUseSystemBtBroadcast": true,
      "isIgnoreVoipResult": true,
      "isSupportDashboard":true,
      "isHideStatusBar": true,
      "instructionWeChatKeyAttributes": "0,0"
    },
    {
      "platform": "BX11", //吉利BX11
      "productid": "BX11",
      "channelid": "BX11",
      "channelname": "",
      "audioSource": "7",
      "voipAudioSource": "7",
      "voiceChannelConfig": "0",
      "messageStreamType": "30",
      "voipStreamType": "10",
      "notificationStreamType": "80",
      "voipRingStreamType": "1000",
      "windowpadding": "0,0,0,0",
      "windowtype": "-1",
      "smartScreenAdapt": "",
      "audiotrackwithnewusage": "1",
      "canSetAudioModeWhenVoip": false,
      "showUserGuide": true,
      "isPalRequestMic": true,
      "isSpeechForField": true,
      "isShieldHyperlinks": false,
      "isModifySystemFont": true,
      "isUseSystemBtBroadcast": true,
      "isShowAutoLogin": false,
      "isIgnoreVoipResult": true,
      "isIgnoreVuiResult": false,
      "isStopLoginWakeup": false,
      "isForegroundProcess": true,
      "isHideStatusBar": true,
      "instructionWeChatKeyAttributes": "1,0"
    },
    {
      "platform": "HX11",  //吉利HX11 Smart
      "productid": "HX11",
      "channelid": "HX11",
      "channelname": "",
      "audioSource": "1",
      "voipAudioSource": "1",
      "voiceChannelConfig": "0",
      "messageStreamType": "30",
      "voipStreamType": "10",
      "notificationStreamType": "80",
      "voipRingStreamType": "1000",
      "windowpadding": "0,0,0,0",
      "windowtype": "2038",
      "smartScreenAdapt": "",
      "audiotrackwithnewusage": "1",
      "canSetAudioModeWhenVoip": true,
      "showUserGuide": true,
      "isPalRequestMic": true,
      "isSpeechForField": true,
      "isModifySystemFont": true,
      "isShieldHyperlinks": false,
      "isShowAutoLogin": false,
      "isStopLoginWakeup": false,
      "isUseSystemBtBroadcast": true,
      "isIgnoreVoipResult": true,
      "isSupportDashboard":false,
      "isHideStatusBar": true,
      "instructionWeChatKeyAttributes": "0,0"
    },
    {
      "platform": "F7-22-SERIALS",               //长城F7-22
      "channelid":"CHANGCHENG_F7_22",
      "channelname": "",
      "audioSource": "1",
      "voipAudioSource": "1",
      "voiceChannelConfig": "1",
      "messageStreamType":"12",
      "voipStreamType":"0",
      "notificationStreamType":"9",
      "voipRingStreamType":"2",
      "windowpadding": "0,0,0,0",
      "windowtype": "2057",
      "audiotrackwithnewusage": "1",
      "canSetAudioModeWhenVoip": false,
      "showUserGuide": true,
      "smartScreenAdapt": "",
      "windowsizeoffset": "464,0,0,0",
      "returnMicFocusDelay": true,
      "instructionWeChatKeyAttributes": "1,0",
      "doaEnable": true,
      "isModifySystemFont": true,
      "isIgnoreVuiResult": false,
      "isShowAutoLogin": true,
      "isSupportNotDisturb": true
    },
    {
      "platform": "Salvator-X-r8a7796-0008",         //长城EC01
      "channelid": "CHANGCHENG_EC01",
      "channelname": "",
      "audioSource": "1",
      "voipAudioSource": "1",
      "voiceChannelConfig": "1",
      "messageStreamType": "9",
      "voipStreamType": "14",
      "notificationStreamType": "9",
      "voipRingStreamType": "14",
      "windowpadding": "0,0,0,0",
      "windowtype": "2057",
      "audiotrackwithnewusage": "1",
      "canSetAudioModeWhenVoip": false,
      "showUserGuide": true,
      "smartScreenAdapt": "",
      "windowsizeoffset": "0,0,0,0",
      "returnMicFocusDelay": true,
      "doaEnable": true,
      "isModifySystemFont": true,
      "instructionWeChatKeyAttributes": "1,0",
      "isShowAutoLogin": true,
      "isSupportNotDisturb": true,
      "isIgnoreVuiResult": false
    },
    {
      "platform": "bmw_idc23 for arm64",    //宝马
      "channelid":"BMW_IDC23",
      "channelname": "",
      "audioSource": "1",
      "voipAudioSource": "1",
      "voiceChannelConfig": "0",
      "messageStreamType":"10",
      "voipStreamType":"3",
      "notificationStreamType":"5",
      "voipRingStreamType":"3",
      "windowpadding": "0,0,0,0",
      "windowtype": "-1",
      "audiotrackwithnewusage": "1",
      "canSetAudioModeWhenVoip": true,
      "showUserGuide": true,
      "smartScreenAdapt": "",
      "windowsizeoffset": "0,0,0,0",
      "isSupportTermsOfUseSpeedLock": true,
      "termsOfUseSpeedLockMaskViewTextColor": "#B08170",
      "maxTermsOfUseSpeedLockValue": 8,
      "termsOfUseSpeedLockMaskViewVisibleRatio": 0.2,
      "instructionWeChatKeyAttributes": "0,3",
      "customGuidingTips": "{\"keyname\":\"语音键\",\"openwechat\":\"播报消息\",\"readmsg\":\"回复消息\",\"answervoip\":\"接听通话\",\"closewechat\":\"关闭微信\",\"ignoremsg\":\"忽略消息\",\"breakvoip\":\"挂断通话\",\"title\":\"方向盘按键使用说明\"}",
      "isPalRequestMic": true,
      "returnMicFocusDelay": true,
      "isQrCodeSpeedLock": true,
      "isUseSystemBtBroadcast": true,
      "isSupportSpeedRule": true,
      "maxSpeedRuleValue": 8,
      "isIgnoreVuiResult": false,
      "isSpeechForField": true,
      "isSupportVoipMinimizeWindowHide": true,
      "isSetFlagOfNotFocusable": true,
      "voipFinishFocusDelayValue": 1000,
      "isOpenTaesLog": true,
      "isShowSystemOverlayWindow": false
    },
    {
      "platform": "S311_MCA", // 梧桐S311_MCA
      "channelid": "S311_MCA",
      "channelname": "",
      "audioSource": "1",
      "voipAudioSource": "1",
      "voiceChannelConfig": "1",
      "messageStreamType": "9",
      "voipStreamType": "0",
      "notificationStreamType": "1",
      "voipRingStreamType": "0",
      "windowpadding": "0,0,0,0",
      "windowtype": "-1",
      "audiotrackwithnewusage": "0",
      "canSetAudioModeWhenVoip": false,
      "showUserGuide": true,
      "smartScreenAdapt": ""
    },
    {
      "platform": "S311_ICA2", // 梧桐S311_ICA2
      "channelid": "S311_ICA2",
      "channelname": "",
      "audioSource": "1",
      "voipAudioSource": "1",
      "voiceChannelConfig": "1",
      "messageStreamType": "9",
      "voipStreamType": "0",
      "notificationStreamType": "1",
      "voipRingStreamType": "0",
      "windowpadding": "0,0,0,0",
      "windowtype": "-1",
      "audiotrackwithnewusage": "0",
      "canSetAudioModeWhenVoip": false,
      "showUserGuide": true,
      "smartScreenAdapt": "",
      "instructionWeChatKeyAttributes": "1,0"
    },
    {
      "platform": "S111_ICA2", // 梧桐S111_ICA2
      "channelid": "S111_ICA2",
      "channelname": "",
      "audioSource": "1",
      "voipAudioSource": "1",
      "voiceChannelConfig": "1",
      "messageStreamType": "9",
      "voipStreamType": "0",
      "notificationStreamType": "1",
      "voipRingStreamType": "0",
      "windowpadding": "0,0,0,0",
      "windowtype": "-1",
      "audiotrackwithnewusage": "0",
      "canSetAudioModeWhenVoip": false,
      "showUserGuide": true,
      "smartScreenAdapt": "",
      "instructionWeChatKeyAttributes": "1,0"
    },
    {
      "platform": "X95", // 奇瑞X95
      "channelid": "X95",
      "channelname": "",
      "audioSource": "1",
      "voipAudioSource": "1",
      "voiceChannelConfig": "1",
      "messageStreamType": "4",
      "voipStreamType": "0",
      "notificationStreamType": "1",
      "voipRingStreamType": "0",
      "windowpadding": "0,0,0,0",
      "windowtype": "-1",
      "audiotrackwithnewusage": "0",
      "canSetAudioModeWhenVoip": false,
      "showUserGuide": true,
      "smartScreenAdapt": "",
      "instructionWeChatKeyAttributes": "1,0"
    },
    {
      "platform": "AOSP on Harman Platform", // 奥迪HCP3
      "channelid": "Full Android on hcp3",
      "channelname": "",
      "audioSource": "1",
      "voipAudioSource": "1",
      "voiceChannelConfig": "0",
      "messageStreamType": "4",
      "voipStreamType": "0",
      "notificationStreamType": "2",
      "voipRingStreamType": "2",
      "windowpadding": "0,0,0,0",
      "windowtype": "2038",
      "audiotrackwithnewusage": "1",
      "canSetAudioModeWhenVoip": false,
      "smartScreenAdapt": "",
      "showUserGuide": true,
      "returnMicFocusDelay": true,
      "isSupportLicenseAuth": true
    },
    {
      "platform": "X70_3.0R",//奇瑞梧桐盟博X70_3.0R
      "channelid": "X70_3.0R",
      "channelname": "",
      "audioSource": "1",
      "voipAudioSource": "1",
      "voiceChannelConfig": "1",
      "messageStreamType": "9",
      "voipStreamType": "0",
      "notificationStreamType": "5",
      "voipRingStreamType": "2",
      "windowpadding": "0,0,0,0",
      "windowtype": "-1",
      "audiotrackwithnewusage": "1",
      "canSetAudioModeWhenVoip": true,
      "showUserGuide": true,
      "smartScreenAdapt": ""
    },
    {
      "platform": "sm6150_au for arm64",//梧桐现代POC
      "channelid": "sm6150_au for arm64",
      "channelname": "",
      "audioSource": "1",
      "voipAudioSource": "1",
      "voiceChannelConfig": "1",
      "messageStreamType": "9",
      "voipStreamType": "0",
      "notificationStreamType": "5",
      "voipRingStreamType": "0",
      "windowpadding": "0,0,0,0",
      "windowtype": "-1",
      "audiotrackwithnewusage": "1",
      "canSetAudioModeWhenVoip": true,
      "showUserGuide": false,
      "smartScreenAdapt": ""
    },
    {
      "platform": "bmw_idc23 CN for arm64",    //宝马
      "channelid":"BMW_IDC23",
      "channelname": "",
      "audioSource": "1",
      "voipAudioSource": "1",
      "voiceChannelConfig": "0",
      "messageStreamType":"10",
      "voipStreamType":"3",
      "notificationStreamType":"5",
      "voipRingStreamType":"3",
      "windowpadding": "0,0,0,0",
      "windowtype": "-1",
      "audiotrackwithnewusage": "1",
      "canSetAudioModeWhenVoip": true,
      "showUserGuide": true,
      "smartScreenAdapt": "",
      "windowsizeoffset": "0,0,0,0",
      "isSupportTermsOfUseSpeedLock": true,
      "termsOfUseSpeedLockMaskViewTextColor": "#B08170",
      "maxTermsOfUseSpeedLockValue": 8,
      "termsOfUseSpeedLockMaskViewVisibleRatio": 0.2,
      "instructionWeChatKeyAttributes": "0,3",
      "customGuidingTips": "{\"keyname\":\"语音键\",\"openwechat\":\"播报消息\",\"readmsg\":\"回复消息\",\"answervoip\":\"接听通话\",\"closewechat\":\"关闭微信\",\"ignoremsg\":\"忽略消息\",\"breakvoip\":\"挂断通话\",\"title\":\"方向盘按键使用说明\"}",
      "isPalRequestMic": true,
      "returnMicFocusDelay": true,
      "isQrCodeSpeedLock": true,
      "isUseSystemBtBroadcast": true,
      "isSupportSpeedRule": true,
      "maxSpeedRuleValue": 8,
      "isIgnoreVuiResult": false,
      "isSpeechForField": true,
      "isSupportVoipMinimizeWindowHide": true,
      "isSetFlagOfNotFocusable": true,
      "voipFinishFocusDelayValue": 1000,
      "isOpenTaesLog": true,
      "isShowSystemOverlayWindow": false
    },
    {
      "platform": "VOYAH H53",
      "productid": "VOYAH H53",
      "channelid":"VOYAH H53",
      "channelname": "",
      "audioSource": "1",
      "voipAudioSource": "1",
      "voiceChannelConfig": "0",
      "messageStreamType": "17",
      "voipStreamType": "38",
      "notificationStreamType": "5",
      "voipRingStreamType": "6",
      "windowpadding": "0,0,0,0",
      "windowtype": "-1",
      "smartScreenAdapt": "",
      "audiotrackwithnewusage": "1",
      "canSetAudioModeWhenVoip": true,
      "showUserGuide": true,
      "isUseSystemBtBroadcast": true,
      "isPalRequestMic": true,
      "returnMicFocusDelay": true,
      "palBTCall":true,
      "isSpeechForField": true,
      "isHideStatusBar": true,
      "isIgnoreVuiResult": false,
      "isStopLoginWakeup": false,
      "isShowAutoLogin": true,
      "voipShrinkWindowStickMargin": "150,0,0,0",
      "instructionWeChatKeyAttributes": "1,4"  //配置方向盘微信键属性（右边，五角星icon）
    },
    {
      "platform": "M18",  //东风M18 猛士
      "channelid": "M18",
      "channelname": "",
      "audioSource": "1",
      "voipAudioSource": "1",
      "voiceChannelConfig": "0",
      "messageStreamType": "3",
      "voipStreamType": "3",
      "notificationStreamType": "3",
      "voipRingStreamType": "3",
      "windowpadding": "0,0,0,0",
      "windowtype": "-1",
      "audiotrackwithnewusage": "1",
      "canSetAudioModeWhenVoip": true,
      "showUserGuide": true,
      "isShowAutoLogin": true,
      "isPalRequestMic": true,
      "isSpeechForField": true,
      "isSupportDashboard": true,
      "isIgnoreVuiResult": false,
      "smartScreenAdapt": "",
      "instructionWeChatKeyAttributes": "1,0"  //配置方向盘微信键属性（左边，微信icon）
    },
    {
      "platform": "东风风神",   //m57
      "channelid": "东风风神",
      "channelname": "",
      "audioSource": "1",
      "voiceChannelConfig": "0",
      "messageStreamType": "3",
      "voipStreamType": "3",
      "notificationStreamType": "3",
      "voipRingStreamType": "3",
      "windowpadding": "0,0,0,0",
      "windowtype": "-1",
      "audiotrackwithnewusage": "1",
      "canSetAudioModeWhenVoip": true,
      "showUserGuide": true,
      "isPalRequestMic": true,
      "isSpeechForField": true,
      "smartScreenAdapt": ""
    },
    {
      "platform": "GWM-V3E-r8a7795",//长城H3E
      "channelid": "GWM-V3E-r8a7795",
      "channelname": "",
      "audioSource": "1",
      "voipAudioSource": "1",
      "voiceChannelConfig": "0",
      "messageStreamType": "12",
      "voipStreamType": "13",
      "notificationStreamType": "11",
      "voipRingStreamType": "3",
      "windowpadding": "0,0,0,0",
      "windowtype": "-1",
      "audiotrackwithnewusage": "1",
      "canSetAudioModeWhenVoip": true,
      "showUserGuide": true,
      "smartScreenAdapt": "",
      "returnMicFocusDelay": true,
      "instructionWeChatKeyAttributes": "1,1",
      "isPalRequestMic": true,
      "isIgnoreVuiResult": false
    },
    {
      "platform": "G6SA-23MM",  //广汽丰田23MM
      "channelid": "G6SA-23MM",
      "channelname": "",
      "audioSource": "1",
      "voipAudioSource": "1",
      "voiceChannelConfig": "0",
      "messageStreamType": "9",
      "voipStreamType": "0",
      "notificationStreamType": "5",
      "voipRingStreamType": "2",
      "windowpadding": "0,0,0,0",
      "windowtype": "-1",
      "audiotrackwithnewusage": "1",
      "canSetAudioModeWhenVoip": true,
      "showUserGuide": true,
      "smartScreenAdapt": "",
      "isUseSystemBtBroadcast": true,
      "isPalRequestMic": true,
      "isQrCodeSpeedLock": true,
      "isShowAutoLogin":true,
      "isSpeechForField": true,
      "qrCodePageMaxSpeedLockValue": "8"
    },
    {
      "platform": "BJ60", //北汽BJ60
      "channelid": "BJ60",
      "channelname": "",
      "audioSource": "1",
      "voipAudioSource": "1",
      "voiceChannelConfig": "1",
      "messageStreamType": "9",
      "voipStreamType": "14",
      "notificationStreamType": "5",
      "voipRingStreamType": "14",
      "windowpadding": "0,0,0,0",
      "windowtype": "2020",
      "audiotrackwithnewusage": "1",
      "canSetAudioModeWhenVoip": true,
      "showUserGuide": true,
      "smartScreenAdapt": "",
      "instructionWeChatKeyAttributes": "0,0"
    },
    {
      "platform": "es33", //上汽零束es33
      "channelid": "es33",
      "channelname": "",
      "audioSource": "7",
      "voipAudioSource": "7",
      "voiceChannelConfig": "0",
      "messageStreamType": "3",
      "voipStreamType": "0",
      "notificationStreamType": "5",
      "voipRingStreamType": "2",
      "windowpadding": "0,0,0,0",
      "windowtype": "-1",
      "audiotrackwithnewusage": "1",
      "canSetAudioModeWhenVoip": true,
      "showUserGuide": true,
      "smartScreenAdapt": "",
      "instructionWeChatKeyAttributes": "1,4",
      "isPalRequestMic": true,
      "isSupportNotDisturb": true,
      "isIgnoreVuiResult": false,
      "customGuidingTips": "{\"keyname\":\"微信键\",\"openwechat\":\"打开微信\",\"readmsg\":\"播报消息\",\"answervoip\":\"接听通话\",\"closewechat\":\"关闭微信\",\"ignoremsg\":\"忽略消息\",\"breakvoip\":\"挂断通话\",\"title\":\"将方向盘图示按键设置为\\\"微信\\\"后可实现以下功能\"}"
    },
    {
      "platform": "TCC8050_AVN", //凯翼项目
      "channelid": "TCC8050_AVN",
      "channelname": "",
      "audioSource": "1",
      "voipAudioSource": "1",
      "voiceChannelConfig": "0",
      "messageStreamType": "8",
      "voipStreamType": "0",
      "notificationStreamType": "5",
      "voipRingStreamType": "2",
      "windowpadding": "0,0,0,0",
      "windowtype": "-1",
      "audiotrackwithnewusage": "1",
      "canSetAudioModeWhenVoip": true,
      "showUserGuide": true,
      "smartScreenAdapt": "",
      "instructionWeChatKeyAttributes": "0,0",
      "isPalRequestMic": true,
      "isIgnoreVuiResult": false
    },
    {
      "platform": "CNS30GP", //上汽大众GP
      "channelid": "CNS30GP",
      "channelname": "",
      "audioSource": "1",
      "voiceChannelConfig": "0",
      "messageStreamType": "8",
      "voipStreamType": "0",
      "notificationStreamType": "5",
      "voipRingStreamType": "2",
      "windowpadding": "0,0,0,0",
      "windowtype": "-1",
      "audiotrackwithnewusage": "1",
      "canSetAudioModeWhenVoip": true,
      "showUserGuide": true,
      "smartScreenAdapt": "",
      "instructionWeChatKeyAttributes": "0,0"
    },
    {
      "platform": "harman",  //fca m189
      "channelid": "harman",
      "channelname": "harman",
      "audioSource": "1",
      "voipAudioSource": "1",
      "voiceChannelConfig": "0",
      "messageStreamType": "30",
      "voipStreamType": "10",
      "notificationStreamType": "5",
      "voipRingStreamType": "1000",
      "windowpadding": "0,0,0,0",
      "windowtype": "2038",
      "audiotrackwithnewusage": "1",
      "canSetAudioModeWhenVoip": true,
      "showUserGuide": true,
      "smartScreenAdapt": "",
      "isIgnoreVuiResult": false,
      "isPalRequestMic": true,
      "instructionWeChatKeyAttributes": "1,0"
    },
    {
      "platform": "SA8155 Digital Cockpit",   //东风M18
      "channelid": "SA8155 Digital Cockpit",
      "channelname": "",
      "audioSource": "1",
      "voipAudioSource": "1",
      "voiceChannelConfig": "0",
      "messageStreamType": "3",
      "voipStreamType": "3",
      "notificationStreamType": "3",
      "voipRingStreamType": "3",
      "windowpadding": "0,0,0,0",
      "windowtype": "-1",
      "audiotrackwithnewusage": "1",
      "canSetAudioModeWhenVoip": true,
      "showUserGuide": false,
      "smartScreenAdapt": "",
      "instructionWeChatKeyAttributes": "1,0"
    },
    {
      "platform": "DesaySV",  //奇瑞T22
      "channelid": "DesaySV",
      "channelname": "",
      "audioSource": "1",
      "voipAudioSource": "1",
      "voiceChannelConfig": "0",
      "messageStreamType": "3",
      "voipStreamType": "3",
      "notificationStreamType": "3",
      "voipRingStreamType": "3",
      "windowpadding": "0,0,0,0",
      "windowtype": "-1",
      "audiotrackwithnewusage": "1",
      "canSetAudioModeWhenVoip": true,
      "showUserGuide": true,
      "smartScreenAdapt": "",
      "isPalRequestMic": true,
      "doaEnable": true,
      "isHideNavBarInAgreementUI": true,
      "windowsizeoffset": "0,0,0,50",
      "instructionWeChatKeyAttributes": "1,4"
    },
    {
      "platform": "spm8666p1_64_car",
      "channelid": "spm8666p1_64_car",
      "channelname": "",
      "audioSource": "1",
      "voipAudioSource": "1",
      "voiceChannelConfig": "1",
      "messageStreamType": "3",
      "voipStreamType": "3",
      "notificationStreamType": "3",
      "voipRingStreamType": "3",
      "windowpadding": "0,0,0,0",
      "windowtype": "-1",
      "audiotrackwithnewusage": "1",
      "canSetAudioModeWhenVoip": true,
      "showUserGuide": false,
      "smartScreenAdapt": ""
    },{
      "platform": "arm64 gull",  //大众poc
      "channelid":"DZPOC",
      "channelname": "",
      "audioSource": "1",
      "voipAudioSource": "1",
      "voiceChannelConfig": "0",
      "messageStreamType":"0",
      "voipStreamType":"0",
      "notificationStreamType":"5",
      "voipRingStreamType":"2",
      "windowpadding": "0,0,0,0",
      "windowtype": "-1",
      "audiotrackwithnewusage": "1",
      "canSetAudioModeWhenVoip": true,
      "showUserGuide": false,
      "smartScreenAdapt": "",
      "windowsizeoffset": "0,0,0,0",
      "isPalRequestMic": true
    },
    {
      "platform": "spm8666bp1_64_car",  //被集成项目
      "channelid": "spm8666bp1_64_car",
      "channelname": "",
      "audioSource": "1",
      "voipAudioSource": "1",
      "voiceChannelConfig": "0",
      "messageStreamType": "3",
      "voipStreamType": "3",
      "notificationStreamType": "3",
      "voipRingStreamType": "3",
      "windowpadding": "0,0,0,0",
      "windowtype": "-1",
      "audiotrackwithnewusage": "1",
      "canSetAudioModeWhenVoip": true,
      "showUserGuide": false,
      "smartScreenAdapt": ""
    },
    {
      "platform": "ep35", //上汽零束ep35
      "channelid": "ep35",
      "channelname": "",
      "audioSource": "7",
      "voipAudioSource": "7",
      "voiceChannelConfig": "0",
      "messageStreamType": "3",
      "voipStreamType": "0",
      "notificationStreamType": "5",
      "voipRingStreamType": "2",
      "windowpadding": "0,0,0,0",
      "windowtype": "-1",
      "audiotrackwithnewusage": "1",
      "canSetAudioModeWhenVoip": true,
      "showUserGuide": true,
      "smartScreenAdapt": "",
      "instructionWeChatKeyAttributes": "1,4",
      "isPalRequestMic": true,
      "isSupportNotDisturb": true,
      "isIgnoreVuiResult": false,
      "customGuidingTips": "{\"keyname\":\"微信键\",\"openwechat\":\"打开微信\",\"readmsg\":\"播报消息\",\"answervoip\":\"接听通话\",\"closewechat\":\"关闭微信\",\"ignoremsg\":\"忽略消息\",\"breakvoip\":\"挂断通话\",\"title\":\"将方向盘图示按键设置为\\\"微信\\\"后可实现以下功能\"}"
    },
    {
      "platform": "S311_MCA1_P",
      "channelid": "S311_MCA1_P",
      "channelname": "",
      "audioSource": "1",
      "voipAudioSource": "1",
      "voiceChannelConfig": "1",
      "messageStreamType": "9",
      "voipStreamType": "0",
      "notificationStreamType": "16",
      "voipRingStreamType": "2",
      "windowpadding": "0,0,0,0",
      "windowtype": "-1",
      "audiotrackwithnewusage": "1",
      "canSetAudioModeWhenVoip": true,
      "showUserGuide": true,
      "smartScreenAdapt": ""
    },
    {
      "platform": "G7PH_", // 大众poc
      "channelid": "msmnile_gvmq for arm64",
      "channelname": "",
      "audioSource": "1",
      "voipAudioSource": "1",
      "voiceChannelConfig": "0",
      "messageStreamType": "0",
      "voipStreamType": "0",
      "notificationStreamType": "5",
      "voipRingStreamType": "2",
      "windowpadding": "0,0,0,0",
      "windowtype": "-1",
      "audiotrackwithnewusage": "1",
      "canSetAudioModeWhenVoip": true,
      "showUserGuide": false,
      "isPalRequestMic": true,
      "smartScreenAdapt": "",
      "isOpenLogcatLog": false
    },{
      "platform": "X50",  // 奇瑞X50 14版本
      "channelid": "X50",
      "channelname": "",
      "audioSource": "1",
      "voiceChannelConfig": "0",
      "messageStreamType": "1",
      "voipStreamType": "0",
      "notificationStreamType": "5",
      "voipRingStreamType": "1",
      "windowpadding": "0,0,0,0",
      "windowtype": "-1",
      "audiotrackwithnewusage": "1",
      "canSetAudioModeWhenVoip": true,
      "showUserGuide": false,
      "smartScreenAdapt": "",
      "isPalRequestMic": true
    }
  ]
}