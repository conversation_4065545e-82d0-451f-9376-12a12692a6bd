{"getLoginStatusPollingInterval": 15000, "getRefreshAccountPollingInterval": 60000, "getRefreshContractPollingInterval": 60000, "pollingIntervalRateBackground": 5, "errorMsg": {"1": "未知网络错误", "2": "网络请求响应为空", "3": "网络请求解析错误", "4": "网络请求响应失败", "5": "网络请求不成功", "6": "网络请求异常", "7": "网络请求读取失败", "8": "网络请求重试失败", "11": "渠道为空", "12": "wecarId为空", "13": "commonId为空", "14": "userid为空", "15": "登录类型不支持", "16": "JSON解析错误", "17": "未知错误码", "32": "请求超时", "33": "wecarId不存在", "34": "user id为空", "60": "接口鉴权失败", "1200101": "渠道不合法，对应的registerType不存在", "1000007": "deviceId或vehicleId为空", "1001007": "deviceId或vehicleId不正确", "2101017": "设备鉴权失败", "2101301": "加密渠道校验失败", "3900000": "服务端加锁失败", "1000002": "wecarId为空或格式不正确", "1000100": "pkgName格式不正确", "2001100": "pkgName不正确", "1000003": "userId为空或格式不正确", "1100002": "wecarid不存在", "1100003": "userid不存在", "2102002": "车机未登录用户", "2101002": "已登录其他用户", "3200000": "获取小程序码失败", "1000200": "serviceType 为空或格式不正确", "1000001": "channel格式不正确", "2101090": "check鉴权失败"}}