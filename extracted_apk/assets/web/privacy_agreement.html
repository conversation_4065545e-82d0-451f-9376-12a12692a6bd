<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="Content-Style-Type" content="text/css"/>
    <meta name="viewport"
          content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=no,viewport-fit=cover">
    <title>微信隐私保护指引</title></head>
<body style="background:#ffffff; overflow-x:hidden;">
<div>
    <p style="margin-top:0pt; margin-bottom:0pt; line-height:150%; font-size:12pt">
        <span>更新日期：2022年10月</span>
        <span>31</span><span>日</span>
    </p>
    <p style="margin-top:0pt; margin-bottom:0pt; line-height:150%; font-size:12pt">
        <span>生效日期：2022年10月</span>
        <span>31</span><span>日</span>
    </p>
    <p style="margin-top:0pt; margin-bottom:18pt; text-align:justify; line-height:150%; font-size:12pt">
        <span>&#xa0;</span></p>
    <p style="margin-top:0pt; margin-bottom:0pt; line-height:150%; font-size:12pt">
        <span style="font-weight:bold">为更充分保障你的权利，我们对《</span>
        <span style="font-weight:bold">微信车载版</span>
        <span style="font-weight:bold">隐私保护指引》进行了更新，此版本的更新主要集中于：</span>
    </p>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <span>1. </span><span>更新微信车载版收集信息的类型以及第三方SDK相关信息；</span>
    </p>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <span>2</span><span>. </span><span
    >新增访问、查看、删除、修改个人信息和权限的路径；</span></p>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <span>3. 新增个人信息副本的获得指引、逝者近亲属的权利、注销帐号等操作指引</span><span
    >；</span></p>
    <p style="margin-top:0pt; margin-bottom:18pt; text-align:justify; line-height:150%; font-size:12pt">
        <span>4. 在文末新增历史版本，即《微信隐私保护指引》</span><span
    >。</span></p>
    <h2 style="margin-top:14pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <span>导言</span></h2>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <span>微信</span><span>车载版</span><span
    >是一款由深圳市腾讯计算机系统有限公司（注册地为深圳市南山区粤海街道麻岭社区科技中一路腾讯大厦35层。以下亦简称“我们”或“腾讯”）提供服务的</span><span
    >、专门用于汽车车载系统的微信软件，是微信软件的版本之一，用户通过微信帐号登录微信车载版。</span><span
            style="font-weight:bold">为说明微信</span><span
            style="font-weight:bold">车载版</span><span
            style="font-weight:bold">会如何收集、使用和存储你的个人信息及你享有何种权利，我们将通过本指引向你阐述相关事宜</span><span
    >。</span></p>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <span>为了在本指引下收集你的信息，或者向你提供服务、优化我们的服务以及保障你的帐号安全，我们将需要向你索取相关的权限；其中的敏感权限</span><span
    >例如</span><span
            style="font-weight:bold; text-decoration:underline">麦克风</span><span
            style="font-weight:bold; text-decoration:underline">、悬浮窗权限</span><span
    >等均不会默认开启，只有在你明确同意后我们才会在你同意的范围内调用或使用。对你已经向我们授权的该等权限，</span><span
            style="font-weight:bold">你也可以</span><span
            style="font-weight:bold">根据第五章的指引</span><span
            style="font-weight:bold">管理</span><span
            style="font-weight:bold">你的权限</span><span
            style="font-weight:bold">。</span></p>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <span>需要特别说明的是，我们获得具体某一项敏感权限并不代表我们必然会收集你的相关信息；即使我们已经获得敏感权限，也仅在必要范围内，并在你使用相关的服务或功能期间根据本指引来收集你的相关信息。</span>
    </p>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <span>更加详尽的信息请</span><span>你</span><span
    >根据以下索引阅读相应章节：</span></p>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <a href="https://weixin.qq.com/cgi-bin/readtemplate?lang=zh_CN&amp;t=weixin_agreement&amp;s=privacy#1%E6%88%91%E4%BB%AC%E6%94%B6%E9%9B%86%E3%80%81%E4%BD%BF%E7%94%A8%E7%9A%84%E4%BF%A1%E6%81%AF"
           style="text-decoration:none"><span
                style="color:#0000ff">1.我们收集、使用的信息</span></a></p>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <a href="https://weixin.qq.com/cgi-bin/readtemplate?lang=zh_CN&amp;t=weixin_agreement&amp;s=privacy#2%E4%BF%A1%E6%81%AF%E7%9A%84%E5%AD%98%E5%82%A8"
           style="text-decoration:none"><span style="color:#0000ff">2.信息的存储</span></a>
    </p>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <a href="https://weixin.qq.com/cgi-bin/readtemplate?lang=zh_CN&amp;t=weixin_agreement&amp;s=privacy#3%E4%BF%A1%E6%81%AF%E5%AE%89%E5%85%A8"
           style="text-decoration:none"><span style="color:#0000ff">3.信息安全</span></a>
    </p>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <a href="https://weixin.qq.com/cgi-bin/readtemplate?lang=zh_CN&amp;t=weixin_agreement&amp;s=privacy#4%E4%BF%A1%E6%81%AF%E5%85%B1%E4%BA%AB"
           style="text-decoration:none"><span style="color:#0000ff">4.信息共享</span></a>
    </p>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <a href="https://weixin.qq.com/cgi-bin/readtemplate?lang=zh_CN&amp;t=weixin_agreement&amp;s=privacy#5%E4%BD%A0%E7%9A%84%E6%9D%83%E5%88%A9"
           style="text-decoration:none"><span style="color:#0000ff">5.你的权利</span></a>
    </p>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <a href="https://weixin.qq.com/cgi-bin/readtemplate?lang=zh_CN&amp;t=weixin_agreement&amp;s=privacy#6%E5%8F%98%E6%9B%B4"
           style="text-decoration:none"><span
                style="color:#0000ff">6.变更</span></a></p>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <a href="https://weixin.qq.com/cgi-bin/readtemplate?lang=zh_CN&amp;t=weixin_agreement&amp;s=privacy#7%E6%9C%AA%E6%88%90%E5%B9%B4%E4%BA%BA%E4%BF%9D%E6%8A%A4"
           style="text-decoration:none"><span
                style="color:#0000ff">7.未成年人保护</span></a></p>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <a href="https://weixin.qq.com/cgi-bin/readtemplate?lang=zh_CN&amp;t=weixin_agreement&amp;s=privacy#8%E5%85%B6%E4%BB%96"
           style="text-decoration:none"><span
                style="color:#0000ff">8.其它</span></a></p>
    <p style="margin-top:0pt; margin-bottom:18pt; text-align:justify; line-height:150%; font-size:12pt">
        <a href="https://weixin.qq.com/cgi-bin/readtemplate?lang=zh_CN&amp;t=weixin_agreement&amp;s=privacy#9%E8%81%94%E7%B3%BB%E6%88%91%E4%BB%AC"
           style="text-decoration:none"><span style="color:#0000ff">9.联系我们</span></a>
    </p>
    <h2 style="margin-top:14pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <span>1. 我们收集、使用的信息</span></h2>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <span>在你使用微信</span><span>车载版</span><span
    >及相关</span><span>服务的过程中，我们会按照如下方式收集、使用你在使用服务时主动提供或因为使用服务而产生的信息：</span>
    </p>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <span>1</span><span>.</span><span
    >1</span><span> </span><span
    >当你</span><span>登录、使用</span><span
    >微信</span><span>车载版</span><span
    >时，我们会收集、使用你的昵称、头像，收集这些信息是为了帮助你使用微信</span><span
    >车载版</span><span>通讯的</span><span
    >功能</span><span>。</span><span
    >若你不提供这类信息，你</span><span>将</span><span
    >无法正常使用我们的服务。</span></p>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <span>1.2 当你使用微信</span><span
    >车载版</span><span>时，为保障你正常使用我们的服务，维护我们服务的正常运行，改进及优化我们的服务体验以及保障你的帐号安全，我们会收集、使用你的</span><span
    >设备型号、操作系统、唯一设备标识符、登录IP地址、微信车载版软件版本号、接入网络的方式、类型和状态、网络质量数据、设备加速器（如重力感应设备）、操作日志、服务日志信息（如你在微信车载版查看操作的记录、服务故障信息、引荐网址等信息）等日志信息</span><span
    >，</span><span>同时，</span><span
    >你</span><span>也</span><span
    >可以选择上传</span><span
    >操作日志、服务日志信息等日志信息</span><span
    >协助我们</span><span>帮你</span><span
    >定位和解决问题</span><span>。同时，为了预防病毒、木马程序或其他恶意程序、网站，我们可能会收集你设备安装的应用信息、正在运行的进程信息或设备内存中寄存的数据，以防止你的个人信息泄露。</span>
    </p>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <span>1.3 </span><span style="font-weight:bold">当你使用</span><span
            style="font-weight:bold">发送微信消息</span><span
            style="font-weight:bold">、</span><span
            style="font-weight:bold">拨打微信语音通话、发送“发微信消息、回复、播报”等语音指令</span><span
            style="font-weight:bold">功能时，我们会</span><span
            style="font-weight:bold">在你</span><span
            style="font-weight:bold; text-decoration:underline">授权麦克风权限</span><span
            style="font-weight:bold">后</span><span
            style="font-weight:bold">收集你的语音内容，</span><span
            style="font-weight:bold">这</span><span
            style="font-weight:bold">是为实现</span><span
            style="font-weight:bold">车载微信的大部分功能</span><span
            style="font-weight:bold">所必需的，若你不同意</span><span
            style="font-weight:bold">或者取消授权，你</span><span
            style="font-weight:bold">将无法使用相应功能</span><span
            style="font-weight:bold">。</span><span>我们实时处理之后，向你返回处理结果，不会保存你的数据。</span>
    </p>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <span style="font-weight:bold">1.4 </span><span
            style="font-weight:bold">当你使用播报消息的功能时，我们将在你的语音指令下发后把文字消息转为语音形式，因此我们会收集待播报的文字信息，这是为实现前述功能所必需的。我们实时处理之后，向你返回处理结果，不会保存你的数据。</span>
    </p>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <span>1</span><span>.5 </span><span
            style="font-weight:bold">为保障驾驶安全，减少对你驾驶过程中的影响，微信车载版使用全悬浮窗展示，</span><span
            style="font-weight:bold; text-decoration:underline">我们会</span><span
            style="font-weight:bold; text-decoration:underline">在你授权开启悬浮窗权限后</span><span
            style="font-weight:bold">显示车载微信的页面内容</span><span
            style="font-weight:bold">，</span><span
            style="font-weight:bold">这是实现你获得车载微信展示的内容所必需的，如你不同意或者取消授权，你将无法正常浏览阅读车载微信显示的内容</span><span
    >，但仍不影响你继续使用微信其他版本。</span></p>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <span>1</span><span>.6</span><span
    >当你在第三方主体运营的网站或者应用中使用微信登录功能时，我们将向该等第三方主体提供你的个人信息，具体的对外提供范围以交互页面中你确认的为准。</span>
    </p>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <span>1.7</span><span style="font-weight:bold">我们不会保存你的聊天记录。你的聊天记录会存储在你的终端设备。</span>
    </p>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <span style="font-weight:bold">1</span><span
            style="font-weight:bold">.8 </span><span>如果你联系我们的客服，我们可能需要你提供必要的个人信息进行身份验证，以保证你的帐号安全。为根据你的诉求提供服务，经过你的授权，人工客服人员需要在你授权范围内查询或核验你的相关信息，我们将尽可能采取技术和管理措施保障你的信息安全和保密，并在必要范围内进行使用。</span><span
            style="font-weight:bold">我们可能还会保存你的联系方式（你使用的或主动提供的手机号码、微信号、QQ号或其他联系方式）、你与</span><span
            style="font-weight:bold">客服</span><span
            style="font-weight:bold">的通信记录和内容，以及其他必要信息，以便为你提供和记录客</span><span
            style="font-weight:bold">服</span><span
            style="font-weight:bold">服务</span><span
    >。</span></p>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <span style="font-weight:bold">1.9</span><span>&#xa0;</span><span
            style="font-weight:bold">另外，根据相关法律法规及国家标准，以下情形中，我们可能会处理你的相关个人信息而无需征求你的授权同意：</span>
    </p>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <span style="font-weight:bold">a)</span><span>&#xa0;</span><span
            style="font-weight:bold">为个人信息处理者履行法定义务或法定职责所必需；</span></p>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <span style="font-weight:bold">b)</span><span>&#xa0;</span><span
            style="font-weight:bold">为订立、履行你作为一方当事人的合同所必需；</span></p>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <span style="font-weight:bold">c)</span><span>&#xa0;</span><span
            style="font-weight:bold">为应对突发公共卫生事件，或者紧急情况下为保护自然人的生命健康和财产安全所必需；</span>
    </p>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <span style="font-weight:bold">d)</span><span>&#xa0;</span><span
            style="font-weight:bold">依法在合理的范围内处理你自行公开或者其他已经合法公开的个人信息；</span></p>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <span style="font-weight:bold">e)</span><span>&#xa0;</span><span
            style="font-weight:bold">法律、行政法规规定的其他情形。</span></p>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <span>1.10 请你理解，我们向你提供的功能和服务是不断更新和发展的，如果某一功能或服务未在前述说明中告知收集、使用你的信息的处理规则，我们会通过页面提示、交互流程等方式另行向你告知信息收集的范围、目的及方式，以征得你的同意。</span>
    </p>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <span>1.11 为了确保服务的安全，帮助我们更好地了解我们应用程序的运行情况，我们可能记录相关信息，例如，你使用应用程序的频率、崩溃数据、总体使用情况、</span><span
    >性能数据以及应用程序的来源。我们不会将我们存储在分析软件中的信息与你在应用程序中提供的任何个人身份信息融合处理。</span>
    </p>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <span>1.12 如我们使用你的个人信息，超出了你所授权的目的及具有直接或合理关联的范围，我们将在使用你的个人信息前，再次向你告知并征得你的明示同意。</span>
    </p>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <span>1.13 请你注意，</span><span
    >除本指引明确写明，</span><span>目前微信</span><span
    >车载版</span><span>不会主动从深圳市腾讯计算机系统有限公司外的第三方获取你的个人信息。如未来为业务发展需要从第三方间接获取你个人信息，我们会在获取前向你明示你个人信息的来源、类型及使用范围，如微信</span><span
    >车载版</span><span>开展业务需进行的个人信息处理活动超出你原本向第三方提供个人信息时的授权同意范围，我们将在处理你的该等个人信息前，征得你的明示同意。</span>
    </p>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <span>此外，我们将会严格遵守相关法律法规的规定，并要求第三方保障其提供的信息的合法性。</span></p>
    <p style="margin-top:0pt; margin-bottom:18pt; text-align:justify; line-height:150%; font-size:12pt">
        <span>1.14</span><span
            style="font-weight:bold; background-color:#ffffff">当我们的产品或服务发生停止运营的情形时，我们将以推送通知、公告等形式通知你，在合理的期限内删除你的个人信息或进行匿名化处理，并立即停止收集个人信息的活动。（本指引中的“匿名化处理”，是指通过对个人信息的技术处理，使得个人信息主体无法被识别，且处理后的信息不能被复原的过程。个人信息经匿名化处理后所得的信息不属于个人信息。）</span>
    </p>
    <p style="margin-top:0pt; margin-bottom:6pt; text-align:justify; line-height:150%; widows:0; orphans:0; font-size:12pt">
        <span style="font-weight:bold">请你理解，我们向你提供的功能和服务是不断更新和发展的，如果某一功能或服务未在前述说明中且需要收集你的信息，我们会通过页面提示、交互流程、网站公告等方式另行向你说明信息收集的内容、范围和目的，以征得你的同意。</span>
    </p>
    <p style="margin-top:0pt; margin-bottom:18pt; text-align:justify; line-height:150%; font-size:12pt">
        <span>&#xa0;</span></p>
    <h2 style="margin-top:14pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <span>2. 信息的存储</span></h2>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <span style="font-weight:bold">2.1 信息存储的地点</span></p>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <span style="font-weight:bold">我们会按照法律法规规定，将境内收集的用户个人信息存储于中国境内</span><span
    >&#xa0;</span><span>。如果你的个人信息存储地点从中国境内转移到境外的，我们将严格依照法律的规定执行。</span>
    </p>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <span style="font-weight:bold">2.2 信息存储的期限</span></p>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <span>一般而言，我们仅在为实现目的所必需的时间及法律、行政法规所规定的保存期限内保留你的个人信息</span><span
    >。如果你注销微信帐号，我们将根据相关法律法规规定处理你的个人信息。</span></p>
    <p style="margin-top:0pt; margin-bottom:18pt; text-align:justify; line-height:150%; font-size:12pt">
        <span>当我们的产品或服务发生停止运营的情形时，我们将以公告等形式通知你，并在合理的期限内删除你的个人信息或进行匿名化处理。（本指引中的“匿名化处理”，是指通过对个人信息的技术处理，使得个人信息主体无法被识别，且处理后的信息不能被复原的过程。个人信息经匿名化处理后所得的信息不属于个人信息。）</span>
    </p>
    <h2 style="margin-top:14pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <span>3.信息安全</span></h2>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <span>我们努力为用户的信息安全提供保障，以防止信息的丢失、不当使用、篡改、未经授权访问或披露。</span></p>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <span>我们将在合理的安全水平内使用各种安全保护措施以保障信息的安全。例如，我们会使用加密技术（例如，SSL）、去标识化、匿名化处理等手段来保护你的个人信息。</span>
    </p>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <span>我们通过不断提升的技术手段加强安装在你设备端的软件的安全能力，以防止你的个人信息泄露。例如，我们为了安全传输会在你设备本地完成部分信息加密的工作；为了预防病毒、木马程序或其他恶意程序、网站，我们可能会了解你设备安装的应用信息、正在运行的进程信息或设备内存中寄存的数据；为了预防诈骗、盗号、仿冒他人等不法行为和进行安全检查可能会分析利用</span><span
    >设备信息</span><span>、登录IP地址、操作日志等数据，以便于采取安全措施或进行安全提醒等。</span>
    </p>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <span>我们建立专门的管理制度、操作规程和组织以保障信息的安全。例如，我们严格限制访问信息的人员范围，要求他们遵守保密义务，并进行审计。此外，腾讯还会设立主要由外部成员组成的独立机构对我们的个人信息保护情况进行监督。</span>
    </p>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <span>若发生个人信息泄露等安全事件，我们会启动应急预案及补救措施，阻止安全事件扩大，并依法履行相关的报告及通知义务。</span>
    </p>
    <p style="margin-top:0pt; margin-bottom:18pt; text-align:justify; line-height:150%; font-size:12pt">
        <span>我们会尽力保护你的个人信息。</span><span
            style="font-weight:bold">我们也请你理解，任何安全措施都无法做到无懈可击</span><span
    >。</span></p>
    <h2 style="margin-top:14pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <span>4.信息共享</span></h2>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <span>4</span><span>.</span><span
    >1</span><span>我们不会向合作伙伴分享可用于识别你个人身份的信息，除非你明确授权。</span>
    </p>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <span>4</span><span>.</span><span
    >2</span><span>随着我们业务的持续发展，我们有可能进行合并、分立等交易，若涉及个人信息转移的，我们将告知你相关情形，按照法律法规及不低于本指引所要求的标准继续保护或要求新的处理者继续保护你的个人信息。</span>
    </p>
    <p style="margin-top:0pt; margin-bottom:18pt; text-align:justify; line-height:150%; font-size:12pt">
        <span>4.3</span><span style="font-weight:bold">为确保部分功能的实现，使你能够使用和享受更多的服务及功能，我们会在应用中嵌入第三方的SDK，我们将审慎评估该等SDK的使用目的。为了便于你在驾驶过程中获取信息，降低驾驶危险，</span><span
            style="font-weight:bold; text-decoration:underline">我们会委托腾讯云计算（北京）有限责任公司运营的</span><span
            style="font-weight:bold; text-decoration:underline">TencentCar</span><span
            style="font-weight:bold; text-decoration:underline"> SDK</span><span
            style="font-weight:bold; text-decoration:underline">、</span><span
            style="font-weight:bold; text-decoration:underline">WeIM</span><span
            style="font-weight:bold; text-decoration:underline"> SDK</span><span
            style="font-weight:bold; text-decoration:underline">按照以下操作收集或使用你的相关信息。</span>
    </p>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; widows:0; orphans:0; font-size:12pt">
        <span style="font-weight:bold">W</span><span
            style="font-weight:bold">eIM</span><span
            style="font-weight:bold"> SDK:</span></p>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; widows:0; orphans:0; font-size:12pt">
        <span style="font-weight:bold">第三方主体：腾讯云计算（北京）有限责任公司</span></p>
    <p style="margin-top:0pt; margin-bottom:0pt; line-height:150%; font-size:12pt"><span
            style="font-weight:bold">共享的信息：</span><span
            style="font-weight:bold">语音通话状态、来消息/微信通话联系人昵称、头像等</span></p>
    <p style="margin-top:0pt; margin-bottom:0pt; line-height:150%; font-size:12pt"><span
            style="font-weight:bold">使用场景：</span><span
            style="font-weight:bold">&#xa0;</span><span
            style="font-weight:bold">用户使用车载微信过程中</span></p>
    <p style="margin-top:0pt; margin-bottom:0pt; line-height:150%; font-size:12pt"><span
            style="font-weight:bold">使用目的：</span><span
            style="font-weight:bold">&#xa0;</span><span
            style="font-weight:bold">传输并展示于汽车相关界面上</span></p>
    <p style="margin-top:0pt; margin-bottom:0pt; line-height:150%; font-size:12pt"><span
            style="font-weight:bold">共享方式：</span><span
            style="font-weight:bold">&#xa0;</span><span
            style="font-weight:bold">本地传输</span><br/>
        <span
                style="font-weight:bold">第三方个人信息处理规则：<a
                href="https://privacy.qq.com/document/preview/5425be4d1ecb4b55b3aab6afacb80e24?from_wecom=1"
                style="text-decoration:none"><span
                style="color:#0000ff">https://privacy.qq.com/document/preview/5425be4d1ecb4b55b3aab6afacb80e24?from_wecom=1</span></a></span>
    </p>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; widows:0; orphans:0; font-size:12pt">
        <span style="font-weight:bold">&#xa0;</span></p>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; widows:0; orphans:0; font-size:12pt">
        <span style="font-weight:bold">TencentCar</span><span
            style="font-weight:bold"> SDK:</span></p>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; widows:0; orphans:0; font-size:12pt">
        <span style="font-weight:bold">第三方主体：腾讯云计算（北京）有限责任公司</span></p>
    <p style="margin-top:0pt; margin-bottom:0pt; line-height:150%; font-size:12pt"><span
            style="font-weight:bold">共享的信息：</span><span
            style="font-weight:bold">用户语音指令、车辆识别信息（车辆识别码，</span><span
            style="font-weight:bold">deviceid</span><span
            style="font-weight:bold">）</span></p>
    <p style="margin-top:0pt; margin-bottom:0pt; line-height:150%; font-size:12pt"><span
            style="font-weight:bold">使用场景：</span><span
            style="font-weight:bold">&#xa0;</span><span
            style="font-weight:bold">用户使用语音指令操作页面（用户语音指令）；应用启动（车辆识别信息）</span></p>
    <p style="margin-top:0pt; margin-bottom:0pt; line-height:150%; font-size:12pt"><span
            style="font-weight:bold">使用目的： 根据用户发送的指令进行相关操作运行（用户语音指令）；应用鉴权（车辆识别信息）</span>
    </p>
    <p style="margin-top:0pt; margin-bottom:0pt; line-height:150%; font-size:12pt"><span
            style="font-weight:bold">共享方式：</span><span
            style="font-weight:bold">&#xa0;</span><span
            style="font-weight:bold">本地</span><span
            style="font-weight:bold">传输</span><span
            style="font-weight:bold"> </span><br/><span
            style="font-weight:bold">第三方个人信息处理规则：<a
            href="https://privacy.qq.com/document/preview/23c483ad2a6d4ef7970b1f19a5b4f44e?from_wecom=1"
            style="text-decoration:none"><span
            style="color:#0000ff">https://privacy.qq.com/document/preview/23c483ad2a6d4ef7970b1f19a5b4f44e?from_wecom=1</span></a></span>
    </p>
    <p style="margin-top:0pt; margin-bottom:0pt; line-height:150%; font-size:12pt"><span
    >&#xa0;</span></p>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; widows:0; orphans:0; font-size:12pt">
        <span>你可以通过相关链接查看第三方的数据使用和保护规则。请注意，第三方</span><span
    >SDK</span><span>可能由于版本升级、策略调整等原因导致其个人信息处理类型发生变化，请以其公示的官方说明为准。</span>
    </p>
    <p style="margin-top:0pt; margin-bottom:6pt; text-align:justify; line-height:150%; widows:0; orphans:0; font-size:12pt">
        <span>&#xa0;</span></p>
    <h2 style="margin-top:14pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <span>5.你的权利</span></h2>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <span>在你使用微信</span><span>车载版</span><span
    >期间，为了你可以更加便捷地查阅、复制、更正、删除你的个人信息，同时保障你撤回对个人信息处理的同意及注销帐号的权利，我们在产品设计中为你提供了相应的操作设置，你可以</span><span
    >参考下面的指引</span><span>进行</span><span
    >相应</span><span>操作。此外，我们还设置了投诉举报、请求解释的渠道，你的意见及请求将会得到及时的处理。</span>
    </p>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <span style="font-weight:bold">请注意，不同操作系统、软件的不同版本之间，操作设置可能存在差异</span><span
    >；此外，为了优化你的使用体验，我们也可能对操作设置进行调整。故</span><span
    >以上</span><span>指引仅供参考，若你对行使相关权利的方式及途径仍有任何疑问的，你可以通过本规则第9条披露的方式与我们联系。</span>
    </p>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <span style="font-weight:bold">5</span><span
            style="font-weight:bold">.1</span><span
            style="font-weight:bold">访问个人信息（头像、昵称）</span></p>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <span>进入微信车载版后，点击左上角头像，进入设置页即可查看头像和昵称等基本信息。或者在手机设备上登录微信后，按照《微信隐私保护指引》的相关操作查看头像、昵称等基本信息。</span>
    </p>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <span style="font-weight:bold">5</span><span
            style="font-weight:bold">.2 </span><span
            style="font-weight:bold">删除、更正个人信息</span></p>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <span>1)  </span><span
    >在手机设备上登录微信后，点击“我的”；</span></p>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <span>2)  </span><span>点击头像或昵称；</span></p>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <span>3</span><span
    >） 可删除或更换原头像或原昵称。</span></p>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <span style="font-weight:bold">5.3 </span><span
            style="font-weight:bold">选择是否播报会话名称</span></p>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <span>1）</span><span>进入微信车载版后</span><span
    >，</span><span>点击头像进入设置页面</span><span
    >；</span></p>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <span>2）</span><span
    >点击“播报会话名称”右侧的开关图样</span><span>，</span><span
    >可选择开启或关闭“播报会话名称”功能</span><span
    >；</span></p>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <span>3）</span><span
    >“播报会话名称”功能关闭后</span><span
    >，</span><span
    >微信车载版不会再对会话对象名称进行语音播报</span><span
    >，</span><span>仅提示你收到新消息</span><span
    >。</span></p>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <span style="font-weight:bold">5.4 </span><span
            style="font-weight:bold">选择是否开机启动微信</span><span
            style="font-weight:bold">车载版</span></p>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <span>1）</span><span>进入微信车载版后</span><span
    >，</span><span>点击头像进入设置页面</span><span
    >；</span></p>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <span>2）</span><span
    >点击“开机启动微信”右侧的开关图样</span><span>，</span><span
    >可选择开启或关闭“开机启动微信”功能</span><span
    >；</span></p>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <span>3）</span><span
    >“开机启动微信”功能开启后</span><span
    >，</span><span
    >车载终端运行后微信车载版会自动</span><span
    >启动</span><span>登录界面</span><span
    >。</span></p>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <span style="font-weight:bold">5</span><span
            style="font-weight:bold">.</span><span
            style="font-weight:bold">5</span><span
            style="font-weight:bold">查看和修改权限授权</span></p>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <span style="font-weight:bold">基于不同车型的系统差异，具体查看和修改权限的入口以实际为准。你可以通过系统设置页或者联系汽车厂商等方式查看、修改你已授权的权限。</span>
    </p>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <span>&#xa0;</span></p>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <span style="font-weight:bold">5</span><span
            style="font-weight:bold">.6 </span><span
            style="font-weight:bold">注销帐号</span></p>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <span style="font-weight:bold">微信车载版是微信软件的特殊版本，用户登录微信车载版的帐号为微信帐号，如需注销，用户可按照《微信隐私保护指引》“5.你的权利——</span><span
            style="font-weight:bold">5.5 注销帐号</span></p>
    <p style="margin-top:0pt; margin-bottom:18pt; text-align:justify; line-height:150%; font-size:12pt">
        <span style="font-weight:bold">”进行操作。</span></p>
    <p style="margin-top:0pt; margin-bottom:6pt; text-align:justify; line-height:150%; widows:0; orphans:0; font-size:12pt">
        <span style="font-weight:bold">注：当你注销帐号后，我们将按照《中华人民共和国网络安全法》等法律法规的规定留存你的相关信息；超出法定保存期限后，我们将删除或匿名化处理你的个人信息。</span>
    </p>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <span style="font-weight:bold">5.7 </span><span
            style="font-weight:bold">获得个人信息副本</span></p>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <span>如</span><span
    >你需要获得微信车载版个人信息副本</span><span
    >，</span><span>你</span><span
    >可通过腾讯客服官方渠道联系我们，以获取</span><span>微信车载版相关信息的副本，可申请副本内容包括头像、昵称、微信号、登录过的设备、微信车载版版本</span><span
    >。</span></p>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <span style="font-weight:bold">5.8逝者近亲属的权利</span></p>
    <p style="margin-top:0pt; margin-bottom:18pt; text-align:justify; line-height:150%; font-size:12pt">
        <span>如微信</span><span>车载版</span><span
    >用户不幸逝世，其近亲属可通过腾讯客服官方渠道联系我们，以获取相关指引来行使法律规定的合法、正当权益。</span></p>
    <h2 style="margin-top:14pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <span>6.变更</span></h2>
    <p style="margin-top:0pt; margin-bottom:18pt; text-align:justify; line-height:150%; font-size:12pt">
        <span>我们可能会适时对本指引进行修订。当指引的条款发生重大变更时，我们会在你登录及版本更新时以推送通知、弹窗或其他符合法律要求的适当形式向你展示变更后的指引。</span>
    </p>
    <h2 style="margin-top:14pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <span>7.未成年人保护</span></h2>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <span style="font-weight:bold">根据</span><span
            style="font-weight:bold">《</span><span
            style="font-weight:bold">机动车驾驶证申领和使用规定</span><span
            style="font-weight:bold">》，18</span><span
            style="font-weight:bold">周岁以下不得申请机动车驾驶证</span><span
            style="font-weight:bold">，</span><span
            style="font-weight:bold">如你为</span><span
            style="font-weight:bold">18</span><span
            style="font-weight:bold">周岁以下的未成年人</span><span
            style="font-weight:bold">，</span><span
            style="font-weight:bold">请不要驾驶机动车辆</span><span
            style="font-weight:bold">。</span></p>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <span style="font-weight:bold">如你不是实际驾驶人，</span><span
            style="font-weight:bold">你</span><span
            style="font-weight:bold">在使用微信</span><span
            style="font-weight:bold">车载版</span><span
            style="font-weight:bold">服务前，应事先取得你的家长或法定监护人的书面同意。若你是未成年人的监护人，当你对你所监护的未成年人的个人信息有相关疑问时，请通过第9章中的联系方式与我们联系。</span>
    </p>
    <p style="margin-top:0pt; margin-bottom:18pt; text-align:justify; line-height:150%; font-size:12pt">
        <span style="font-weight:bold">特别地，若你是14周岁以下的儿童，我们还专门为你制定了</span><a
            href="https://privacy.qq.com/privacy-children.htm" style="text-decoration:none"><span
            style="font-weight:bold; color:#0000ff">《儿童隐私保护声明》</span></a><span
            style="font-weight:bold">，儿童及其监护人在为14周岁以下的儿童完成帐号注册前，还应仔细阅读腾讯公司专门制定的</span><a
            href="https://privacy.qq.com/privacy-children.htm" style="text-decoration:none"><span
            style="font-weight:bold; color:#0000ff">《儿童隐私保护声明》</span></a><span
            style="font-weight:bold">。只有在取得监护人对</span><a
            href="https://privacy.qq.com/privacy-children.htm" style="text-decoration:none"><span
            style="font-weight:bold; color:#0000ff">《儿童隐私保护声明》</span></a><span
            style="font-weight:bold">的同意后，14周岁以下的儿童方可使用微信</span><span
            style="font-weight:bold">车载版</span><span
            style="font-weight:bold">服务。</span></p>
    <h2 style="margin-top:14pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <span>8.其他</span></h2>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <span>8.1 腾讯</span><a
            href="https://privacy.qq.com/policy/tencent-privacypolicy" style="text-decoration:none"><span
            style="color:#0000ff">《隐私政策》</span></a><span>是腾讯统一适用的一般性隐私条款，其中所规定的用户权利及信息安全保障措施均适用于微信用户。如腾讯</span><a
            href="https://privacy.qq.com/policy/tencent-privacypolicy" style="text-decoration:none"><span
            style="color:#0000ff">《隐私政策》</span></a><span>与本指引存在不一致或矛盾之处，请以本指引为准。</span>
    </p>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <span>8.2 在微信</span><span>车载版</span><span
    >某些具体服务或功能项下，我们可能还会通过交互文案、产品界面等方式向你另行告知处理个人信息的目的、方式或范围，若另行告知的内容与本指引不一致的，以另行告知的内容为准。</span>
    </p>
    <p style="margin-top:0pt; margin-bottom:18pt; text-align:justify; line-height:150%; font-size:12pt">
        <span>8.3</span><span>&#xa0;</span><a
            href="https://privacy.qq.com/privacy-children.htm" style="text-decoration:none"><span
            style="color:#0000ff">《儿童隐私保护声明》</span></a><span
    >是适用于14周岁以下的儿童用户的信息保护规则。在</span><a
            href="https://privacy.qq.com/privacy-children.htm" style="text-decoration:none"><span
            style="color:#0000ff">《儿童隐私保护声明》</span></a><span
    >的适用范围内，如其与腾讯</span><a
            href="https://privacy.qq.com/policy/tencent-privacypolicy" style="text-decoration:none"><span
            style="color:#0000ff">《隐私政策》</span></a><span>、本指引存在不一致或矛盾之处，请以</span><a
            href="https://privacy.qq.com/privacy-children.htm" style="text-decoration:none"><span
            style="color:#0000ff">《儿童隐私保护声明》</span></a><span
    >为准。</span></p>
    <h2 style="margin-top:14pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <span>9.联系我们</span></h2>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <span>我们设立了专门的个人信息保护团队和个人信息保护负责人，如果你对本隐私政策或个人信息保护相关事宜有任何疑问或投诉、建议时，你可以通过以下任一方式与我们联系：</span>
    </p>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <span>1）通过</span><a href="https://kf.qq.com/"
                            style="text-decoration:none"><span
            style="color:#0000ff">https://kf.qq.com/</span></a><span
    >与我们联系；</span></p>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <span>2）将你的问题发送至***********************；</span></p>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <span>3）寄到如下地址：中国广东省深圳市南山区海天二路33号腾讯滨海大厦数据隐私保护部（收），邮编：518054；</span>
    </p>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; font-size:12pt">
        <span>我们将尽快审核所涉问题，并在收到你的投诉反馈后的十五天内予以回复</span><span
    >。</span></p>
    <p style="margin-top:0pt; margin-bottom:18pt; text-align:justify; line-height:150%; font-size:12pt">
        <span>&#xa0;</span></p>
    <p style="margin-top:0pt; margin-left:10pt; margin-bottom:0pt; text-align:right; line-height:150%; font-size:12pt">
        <span style="font-weight:bold">深圳市腾讯计算机系统有限公司</span></p>
    <p style="margin-top:0pt; margin-bottom:18pt; text-align:justify; line-height:150%; font-size:12pt">
        <span>&#xa0;</span></p>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; line-height:150%; widows:0; orphans:0; font-size:12pt">
        <span>&#xa0;</span></p>
    <p style="margin-top:0pt; margin-bottom:0pt; text-align:justify; widows:0; orphans:0; font-size:12pt">
        <span>&#xa0;</span></p>
    <div style="-aw-headerfooter-type:footer-primary"><p
            style="margin-top:0pt; margin-bottom:0pt; widows:0; orphans:0; font-size:9pt"><span
            style="font-family:'等线'">&#xa0;</span></p></div>
</div>
</body>
</html>