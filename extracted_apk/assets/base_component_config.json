{"versionName": "2.0.0", "versionCode": "2", "components": [{"name": "Account", "desc": "账号鉴权服务", "type": "server", "supportFeatures": [{"api": "com.tencent.taes.remote.api.account.IAccountApi", "apiImpl": "com.tencent.taes.remote.impl.account.AccountRemoteApi", "binderName": "com.tencent.taes.remote.api.account.IAccountService$Stub", "binderImpl": "com.tencent.taes.account.server.AccountServiceBinder"}, {"api": "com.tencent.taes.remote.api.account.IPhoneAuthApi", "apiImpl": "com.tencent.taes.remote.impl.account.PhoneAuthRemoteApi", "binderName": "com.tencent.taes.remote.api.account.IAccountService$Stub", "binderImpl": "com.tencent.taes.account.server.AccountServiceBinder"}, {"api": "com.tencent.taes.remote.api.wechatpay.IWeChatPayApi", "apiImpl": "com.tencent.taes.remote.impl.account.WeChatPayRemoteApi", "binderName": "com.tencent.taes.remote.api.account.IAccountService$Stub", "binderImpl": "com.tencent.taes.account.server.AccountServiceBinder"}]}, {"name": "<PERSON><PERSON>", "desc": "长链接服务", "type": "server", "supportFeatures": [{"api": "com.tencent.taes.remote.api.push.IPushApi", "apiImpl": "com.tencent.taes.push.PushRemoteApi", "binderName": "com.tencent.wecarbase.cloud.IPushService$Stub", "binderImpl": "com.tencent.taes.push.server.TaiPushBinder"}, {"api": "com.tencent.taes.remote.api.push.IPushExApi", "apiImpl": "com.tencent.taes.push.PushExRemoteApi", "binderName": "com.tencent.taes.remote.api.push.IPushDispatchService$Stub", "binderImpl": "com.tencent.taes.push.server.PushDispatchBinder"}]}, {"name": "Location", "desc": "定位服务", "type": "server", "actionName": "com.tencent.taes.location.ACTION", "supportFeatures": [{"api": "com.tencent.taes.remote.api.location.ILocationApi", "apiImpl": "com.tencent.taes.remote.impl.location.LocationRemoteApi", "binderName": "com.tencent.taes.remote.api.location.ILocationService$Stub", "binderImpl": "com.tencent.taes.location.server.LocationBinder"}, {"api": "com.tencent.taes.remote.api.wechatlocationshared.IWeChatLocationSharedApi", "apiImpl": "com.tencent.taes.remote.impl.wechatlocationshared.WeChatLocationSharedRemoteApi", "binderName": "com.tencent.taes.remote.api.wechatlocationshared.IWeChatLocationSharedService$Stub", "binderImpl": "com.tencent.taes.location.server.WeChatLocationSharedBinder"}]}, {"name": "TraceReport", "desc": "轨迹上报服务", "type": "server", "supportFeatures": [{"api": "com.tencent.taes.remote.api.tracereport.ITraceReportApi", "apiImpl": "com.tencent.taes.remote.impl.tracereport.TraceReportRemoteApi", "binderName": "com.tencent.taes.remote.api.tracereport.ITraceReportService$Stub", "binderImpl": "com.tencent.taes.tracereport.server.TraceReportBinder"}]}, {"name": "<PERSON>ceShadow", "desc": "设备影子", "type": "server", "supportFeatures": [{"api": "com.tencent.taes.remote.api.shadowdevice.IShowDeviceApi", "apiImpl": "com.tencent.taes.remote.impl.shadowdevice.ShadowDeviceRemoteApi", "binderName": "com.tencent.taes.remote.api.deviceshadow.IDeviceShadowReporter$Stub", "binderImpl": "com.tencent.taes.deviceshadow.DeviceShadowBinder"}]}, {"name": "BizReport", "desc": "业务事件上报", "type": "server", "supportFeatures": [{"api": "com.tencent.taes.remote.api.bizeventreport.IBizEventReportApi", "apiImpl": "com.tencent.taes.remote.impl.bizeventreport.BizEventReportRemoteApi", "binderName": "com.tencent.taes.remote.api.bizreport.IBizEventReporter$Stub", "binderImpl": "com.tencent.taes.bizreport.BizEventReportBinder"}]}, {"name": "Policy", "desc": "VUI/GUI仲裁服务", "type": "server", "supportFeatures": [{"api": "com.tencent.taes.remote.api.policy.IGUIPolicyApi", "apiImpl": "com.tencent.taes.remote.impl.policy.GUIPolicyRemoteApi", "binderName": "com.tencent.taes.policy.impl.GuiPolicyService", "binderImpl": "com.tencent.taes.policy.impl.GuiPolicyService"}]}]}