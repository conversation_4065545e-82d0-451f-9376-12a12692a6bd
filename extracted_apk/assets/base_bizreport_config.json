{"enable": true, "enableDoc": "是否启用联网上报功能，填false会把这个上报功能整个禁止", "netAvailableWindow": 60000, "netAvailableWindowDoc": "认为网络连接处于正常状态的时间窗口，单位毫秒", "urlSuccessBlackList": [], "urlSuccessBlackListDoc": "这里面的域名不会上报请求成功的事件，支持正则的模糊匹配，主要是避免上报心跳轮训等请求频繁的成功结果浪费太多流量", "urlErrorBlackList": [], "urlErrorBlackListDoc": "这里面的域名不会上报请求失败的事件，支持正则的模糊匹配", "eventCodeBlackList": [], "eventCodeBlackListDoc": "这里面的eventCode不会上报", "urlSampleRate": {"*/accountsvc3/getLoginStatus": 100, "*/accountsvc3/getloginqrcode": 100, "*/accountsvc3/authapp": 100, "*/TrackReportShedServer/refreshtoken": 100, "*/cloudtask/getTaskList": 100, "*/accountsvc3/palindex": 100, "*/mqtthalley.wecar.map.qq.com": 100, "*/deviceshadow/getDomainReportConfig": 100}, "urlSampleRateDoc": "对url使用采样率上报，无论成功还是失败都遵循这个采样率，设置100表示上报几率为100/1000也就是10%", "eventCodeSampleRate": {"net_req_fail": 1000, "net_req_v2_fail": 1000}, "eventCodeSampleRateDoc": "给每个eventcode的上报设置采样率，最大值1000，表示上报几率为千分之几, \"eventCodeSampleRate\": {\"net_req_success\": 500, \"net_req_error\": 1000}"}