import sys
import os
import xml.dom.minidom
from xml.dom.minidom import parse

gens = []


class Gen(object):
    def __init__(self, by, scale, path):
        self.by = by
        self.scale = scale
        self.path = path


def main():
    print("file count " + str(len(sys.argv) - 1) + " file [" + str(sys.argv) + "]")
    handleConfig(sys.argv[2])
    handleDom(sys.argv[1])


def handleConfig(path):
    global gens
    dom = parse(path)
    for gen in dom.getElementsByTagName("gen"):
        by = gen.getAttribute("by")
        scale = float(gen.getAttribute("scale"))
        gens.append(Gen(by, scale, mkdirValue(by)))


def mkdirValue(by):
    path = os.getcwd() + "/gens/values-" + by
    if os.path.exists(path):
        return path
    os.makedirs(path)
    return path


def handleDom(path):
    global gens
    for gen in gens:
        oriDom = parse(path)
        dimens = oriDom.getElementsByTagName("dimen")
        dom = createDom(gen, dimens)
        writeDom(gen, dom)


def createDom(gen, dimens):
    dom = xml.dom.minidom.Document()
    root = dom.createElement("resources")
    for dimen in dimens:
        name = dimen.getAttribute("name")
        value = dimen.childNodes[0].data
        unitIndex = value.find("dp")
        if unitIndex == -1:
            unitIndex = value.find("sp")
        unit = value[unitIndex:]
        valueFloat = float(value[0:unitIndex]) * gen.scale
        dimenEle = dom.createElement("dimen")
        dimenEle.setAttribute("name", name)
        dimenEle.appendChild(dom.createTextNode(str(valueFloat) + unit))
        root.appendChild(dimenEle)
    dom.appendChild(root)
    return dom


def writeDom(gen, dom):
    fd = open(gen.path + "/dimens.xml", "w")
    dom.writexml(fd, indent='', addindent='\t', newl="\n", encoding='UTF-8')
    fd.flush()
    fd.close()


if __name__ == '__main__':
    main()
