{"schemaVersion": "1.0.0", "buildSystem": "<PERSON><PERSON><PERSON>", "buildSystemVersion": "7.4.2", "buildPlugin": "org.jetbrains.kotlin.gradle.plugin.KotlinAndroidPluginWrapper", "buildPluginVersion": "1.6.21", "projectSettings": {"isHmppEnabled": true, "isCompatibilityMetadataVariantEnabled": false}, "projectTargets": [{"target": "org.jetbrains.kotlin.gradle.plugin.mpp.KotlinAndroidTarget", "platformType": "androidJvm", "extras": {"android": {"sourceCompatibility": "11", "targetCompatibility": "11"}}}]}